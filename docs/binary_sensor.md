# Binary_Sensor Entities

[← Back to Overview](./index.md)

*Total entities: 91*

## Table of Contents

- [0X00124B001938Efef](#0x00124b001938efef)
- [0X00124B001Bd8128D](#0x00124b001bd8128d)
- [0X00158D00020B5Abb](#0x00158d00020b5abb)
- [0X00158D0002B8C8E7](#0x00158d0002b8c8e7)
- [0X00158D0002E214D4](#0x00158d0002e214d4)
- [0X00158D0002E2697E](#0x00158d0002e2697e)
- [0X00158D0002Ea1003](#0x00158d0002ea1003)
- [0X00158D00030423C7](#0x00158d00030423c7)
- [0X00158D0003140D60](#0x00158d0003140d60)
- [0X00158D000315C387](#0x00158d000315c387)
- [0X00158D000315Fac9](#0x00158d000315fac9)
- [0X00178801064432Ad](#0x00178801064432ad)
- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [Camera](#camera)
- [Chladnicka](#chladnicka)
- [Gw1100A](#gw1100a)
- [Ip](#ip)
- [Iphone](#iphone)
- [Lumi Lumi](#lumi-lumi)
- [Openmediavault](#openmediavault)
- [Pavel](#pavel)
- [Pavels](#pavels)
- [Pi](#pi)
- [Pracka](#pracka)
- [Remote](#remote)
- [Reolink](#reolink)
- [Shelly Klimatizace](#shelly-klimatizace)
- [Susicka](#susicka)
- [Synology](#synology)
- [Ulice](#ulice)
- [Updater](#updater)
- [Version](#version)
- [Wan](#wan)
- [Weather](#weather)
- [Weather Station Loznice Netatmo](#weather-station-loznice-netatmo)
- [Weather Station Pracovna Netatmo](#weather-station-pracovna-netatmo)
- [Yeelight](#yeelight)
- [Zahrada](#zahrada)
- [Zasuvka](#zasuvka)
- [Zasuvky Technicka Mistnost](#zasuvky-technicka-mistnost)
- [Zigbee2Mqtt](#zigbee2mqtt)

## Router CC2531 {#0x00124b001938efef}
- **Entity ID**: `binary_sensor.0x00124b001938efef_router`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Router CC2530 {#0x00124b001bd8128d}
- **Entity ID**: `binary_sensor.0x00124b001bd8128d_router`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: connectivity
    - **restored**: True
    - **supported_features**: 0

## 0x00158d00020b5abb Occupancy {#0x00158d00020b5abb}
- **Entity ID**: `binary_sensor.0x00158d00020b5abb_occupancy`
- **State**: `off`
- **Last Changed**: 2025-06-10 06:38:52
- **Last Updated**: 2025-06-10 06:38:52
- **Attributes**:
    - **device_class**: occupancy

## 0X00158D0002B8C8E7 {#0x00158d0002b8c8e7}
*2 entities*

### 0x00158d0002b8c8e7 vibration
- **Entity ID**: `binary_sensor.0x00158d0002b8c8e7_vibration` | **State**: `unavailable`
- **Key Attributes**: device_class: vibration
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Vibration
- **Entity ID**: `binary_sensor.0x00158d0002b8c8e7_vibration_2` | **State**: `unavailable`
- **Key Attributes**: device_class: vibration

## 0x00158d0002e214d4 Door {#0x00158d0002e214d4}
- **Entity ID**: `binary_sensor.0x00158d0002e214d4_contact`
- **State**: `off`
- **Last Changed**: 2025-06-10 06:37:28
- **Last Updated**: 2025-06-10 06:37:28
- **Attributes**:
    - **device_class**: door

## 0x00158d0002e2697e Occupancy {#0x00158d0002e2697e}
- **Entity ID**: `binary_sensor.0x00158d0002e2697e_occupancy`
- **State**: `off`
- **Last Changed**: 2025-06-10 07:04:17
- **Last Updated**: 2025-06-10 07:04:17
- **Attributes**:
    - **device_class**: occupancy

## 0x00158d0002ea1003 Door {#0x00158d0002ea1003}
- **Entity ID**: `binary_sensor.0x00158d0002ea1003_contact`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **device_class**: door

## Sensor pohybu pracovna occupancy {#0x00158d00030423c7}
- **Entity ID**: `binary_sensor.0x00158d00030423c7_occupancy`
- **State**: `on`
- **Last Changed**: 2025-06-10 07:13:33
- **Last Updated**: 2025-06-10 07:13:33
- **Attributes**:
    - **device_class**: occupancy

## 0x00158d0003140d60 Occupancy {#0x00158d0003140d60}
- **Entity ID**: `binary_sensor.0x00158d0003140d60_occupancy`
- **State**: `off`
- **Last Changed**: 2025-06-10 06:38:47
- **Last Updated**: 2025-06-10 06:38:47
- **Attributes**:
    - **device_class**: occupancy

## 0x00158d000315c387 Door {#0x00158d000315c387}
- **Entity ID**: `binary_sensor.0x00158d000315c387_contact`
- **State**: `off`
- **Last Changed**: 2025-06-09 20:42:34
- **Last Updated**: 2025-06-09 20:42:34
- **Attributes**:
    - **device_class**: door

## 0x00158d000315fac9 Door {#0x00158d000315fac9}
- **Entity ID**: `binary_sensor.0x00158d000315fac9_contact`
- **State**: `off`
- **Last Changed**: 2025-06-09 15:01:37
- **Last Updated**: 2025-06-09 15:01:37
- **Attributes**:
    - **device_class**: door

## 0x00178801064432ad led indication {#0x00178801064432ad}
- **Entity ID**: `binary_sensor.0x00178801064432ad_led_indication`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## 0x84fd27fffea36224 Battery {#0x84fd27fffea36224}
- **Entity ID**: `binary_sensor.0x84fd27fffea36224_battery_low`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **device_class**: battery

## Camera {#camera}
*7 entities*

### camera_zahrada Disk Full
- **Entity ID**: `binary_sensor.camera_zahrada_disk_full` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### camera_zahrada Field Detection
- **Entity ID**: `binary_sensor.camera_zahrada_field_detection` | **State**: `unavailable`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### camera_zahrada Illegal Access
- **Entity ID**: `binary_sensor.camera_zahrada_illegal_access` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### camera_zahrada Line Crossing
- **Entity ID**: `binary_sensor.camera_zahrada_line_crossing` | **State**: `unavailable`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### camera_zahrada Motion
- **Entity ID**: `binary_sensor.camera_zahrada_motion` | **State**: `unavailable`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### camera_zahrada Scene Change Detection
- **Entity ID**: `binary_sensor.camera_zahrada_scene_change_detection` | **State**: `unavailable`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### camera_zahrada Tamper Detection
- **Entity ID**: `binary_sensor.camera_zahrada_tamper_detection` | **State**: `unavailable`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Chladnicka {#chladnicka}
*3 entities*

### Chladnička Door
- **Entity ID**: `binary_sensor.chladnicka_door` | **State**: `off`
- **Key Attributes**: device_class: door

### Chladnička Door open
- **Entity ID**: `binary_sensor.chladnicka_door_open` | **State**: `off`
- **Key Attributes**: device_class: door

### Chladnička Eco friendly
- **Entity ID**: `binary_sensor.chladnicka_eco_friendly` | **State**: `off`

## Gw1100A {#gw1100a}
*3 entities*

### GW1100A Battery 1
- **Entity ID**: `binary_sensor.gw1100a_battery_1` | **State**: `off`
- **Key Attributes**: device_class: battery

### GW1100A Battery 2
- **Entity ID**: `binary_sensor.gw1100a_battery_2` | **State**: `off`
- **Key Attributes**: device_class: battery

### GW1100A Battery 3
- **Entity ID**: `binary_sensor.gw1100a_battery_3` | **State**: `off`
- **Key Attributes**: device_class: battery

## Ip {#ip}
*5 entities*

### camera_ulice Disk Full
- **Entity ID**: `binary_sensor.ip_camera_disk_full` | **State**: `off`
- **Other Attributes**:
    - **last_tripped_time**: 2025-06-08T21:41:37.288020

### camera_ulice Field Detection
- **Entity ID**: `binary_sensor.ip_camera_field_detection` | **State**: `off`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **last_tripped_time**: 2025-06-10T08:37:34.180218

### camera_ulice Line Crossing
- **Entity ID**: `binary_sensor.ip_camera_line_crossing` | **State**: `off`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **last_tripped_time**: 2025-06-10T08:37:05.602387

### camera_ulice Motion
- **Entity ID**: `binary_sensor.ip_camera_motion` | **State**: `off`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **last_tripped_time**: 2025-06-10T09:02:16.029861

### camera_ulice Tamper Detection
- **Entity ID**: `binary_sensor.ip_camera_tamper_detection` | **State**: `off`
- **Key Attributes**: device_class: motion
- **Other Attributes**:
    - **last_tripped_time**: 2025-06-08T21:41:37.288017

## iPhone SE (2nd generation) Focus {#iphone}
- **Entity ID**: `binary_sensor.iphone_se_2nd_generation_focus`
- **State**: `off`
- **Last Changed**: 2025-06-10 05:21:09
- **Last Updated**: 2025-06-10 05:21:09
- **Attributes**:
    - **icon**: mdi:moon-waning-crescent

## sensor-uniku-vody-koupelna-nahore Moisture {#lumi-lumi}
- **Entity ID**: `binary_sensor.lumi_lumi_sensor_wleak_aq1_moisture`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: moisture

## openmediavault_ping {#openmediavault}
- **Entity ID**: `binary_sensor.openmediavault_ping`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **device_class**: connectivity

## Pavel {#pavel}
*8 entities*

### Pavel - iPad (2) Focus
- **Entity ID**: `binary_sensor.pavel_ipad_2_focus` | **State**: `off`

### Pavel - iPad Focus
- **Entity ID**: `binary_sensor.pavel_ipad_focus` | **State**: `off`

### Pavel - iPhone Focus
- **Entity ID**: `binary_sensor.pavel_iphone_focus` | **State**: `off`

### Pavel - MacBook Pro Active
- **Entity ID**: `binary_sensor.pavel_macbook_pro_active` | **State**: `off`
- **Other Attributes**:
    - **Fast User Switched**: False
    - **Idle**: False
    - **Locked**: True
    - **Screen Off**: True
    - **Screensaver**: False
    - **Sleeping**: True
    - **Terminating**: False

### Pavel - MacBook Pro Audio Input In Use
- **Entity ID**: `binary_sensor.pavel_macbook_pro_audio_input_in_use` | **State**: `off`

### Pavel - MacBook Pro Audio Output In Use
- **Entity ID**: `binary_sensor.pavel_macbook_pro_audio_output_in_use` | **State**: `off`

### Pavel - MacBook Pro Camera In Use
- **Entity ID**: `binary_sensor.pavel_macbook_pro_camera_in_use` | **State**: `off`

### Pavel - MacBook Pro Focus
- **Entity ID**: `binary_sensor.pavel_macbook_pro_focus` | **State**: `off`

## Pavels {#pavels}
*5 entities*

### Pavel’s MacBook Pro Active
- **Entity ID**: `binary_sensor.pavels_macbook_pro_active` | **State**: `on`
- **Other Attributes**:
    - **Fast User Switched**: False
    - **Idle**: False
    - **Locked**: False
    - **Screen Off**: False
    - **Screensaver**: False
    - **Sleeping**: False
    - **Terminating**: False

### Pavel’s MacBook Pro Audio Input In Use
- **Entity ID**: `binary_sensor.pavels_macbook_pro_audio_input_in_use` | **State**: `off`

### Pavel’s MacBook Pro Audio Output In Use
- **Entity ID**: `binary_sensor.pavels_macbook_pro_audio_output_in_use` | **State**: `on`

### Pavel’s MacBook Pro Camera In Use
- **Entity ID**: `binary_sensor.pavels_macbook_pro_camera_in_use` | **State**: `off`

### Pavel’s MacBook Pro Focus
- **Entity ID**: `binary_sensor.pavels_macbook_pro_focus` | **State**: `off`

## Pi-Hole Status {#pi}
- **Entity ID**: `binary_sensor.pi_hole_status`
- **State**: `on`
- **Last Changed**: 2025-06-10 07:06:45
- **Last Updated**: 2025-06-10 07:06:45

## Pračka pere - status {#pracka}
- **Entity ID**: `binary_sensor.pracka_pere_status`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: running
    - **icon**: mdi:washing-machine

## binary_sensor.remote_ui {#remote}
- **Entity ID**: `binary_sensor.remote_ui`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Reolink {#reolink}
*4 entities*

### Reolink zahrada Animal
- **Entity ID**: `binary_sensor.reolink_zahrada_animal` | **State**: `off`

### Reolink zahrada Motion
- **Entity ID**: `binary_sensor.reolink_zahrada_motion` | **State**: `off`
- **Key Attributes**: device_class: motion

### Reolink zahrada Person
- **Entity ID**: `binary_sensor.reolink_zahrada_person` | **State**: `off`

### Reolink zahrada Vehicle
- **Entity ID**: `binary_sensor.reolink_zahrada_vehicle` | **State**: `off`

## Shelly Klimatizace {#shelly-klimatizace}
*4 entities*

### shelly-klimatizace Switch 0 overcurrent
- **Entity ID**: `binary_sensor.shelly_klimatizace_switch_0_overcurrent` | **State**: `off`
- **Key Attributes**: device_class: problem

### shelly-klimatizace Switch 0 overheating
- **Entity ID**: `binary_sensor.shelly_klimatizace_switch_0_overheating` | **State**: `off`
- **Key Attributes**: device_class: problem

### shelly-klimatizace Switch 0 overpowering
- **Entity ID**: `binary_sensor.shelly_klimatizace_switch_0_overpowering` | **State**: `off`
- **Key Attributes**: device_class: problem

### shelly-klimatizace Switch 0 overvoltage
- **Entity ID**: `binary_sensor.shelly_klimatizace_switch_0_overvoltage` | **State**: `off`
- **Key Attributes**: device_class: problem

## Sušička suší - status {#susicka}
- **Entity ID**: `binary_sensor.susicka_susi_status`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: running
    - **icon**: mdi:tumble-dryer

## Synology {#synology}
*5 entities*

### Synology (Drive 1) Below min remaining life
- **Entity ID**: `binary_sensor.synology_drive_1_below_min_remaining_life` | **State**: `off`
- **Key Attributes**: device_class: safety
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 1) Exceeded max bad sectors
- **Entity ID**: `binary_sensor.synology_drive_1_exceeded_max_bad_sectors` | **State**: `off`
- **Key Attributes**: device_class: safety
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 2) Below min remaining life
- **Entity ID**: `binary_sensor.synology_drive_2_below_min_remaining_life` | **State**: `off`
- **Key Attributes**: device_class: safety
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 2) Exceeded max bad sectors
- **Entity ID**: `binary_sensor.synology_drive_2_exceeded_max_bad_sectors` | **State**: `off`
- **Key Attributes**: device_class: safety
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Security status
- **Entity ID**: `binary_sensor.synology_security_status` | **State**: `on`
- **Key Attributes**: device_class: safety
- **Other Attributes**:
    - **attribution**: Data provided by Synology
    - **malware**: safe
    - **network**: risk
    - **securitySetting**: safe
    - **systemCheck**: safe
    - **update**: safe
    - **userInfo**: risk

## Ulice {#ulice}
*3 entities*

### Ulice All occupancy
- **Entity ID**: `binary_sensor.ulice_all_occupancy` | **State**: `off`
- **Key Attributes**: device_class: occupancy

### Ulice Motion
- **Entity ID**: `binary_sensor.ulice_motion` | **State**: `off`
- **Key Attributes**: device_class: motion

### Ulice Person occupancy
- **Entity ID**: `binary_sensor.ulice_person_occupancy` | **State**: `off`
- **Key Attributes**: device_class: occupancy

## Updater {#updater}
- **Entity ID**: `binary_sensor.updater`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: update
    - **restored**: True
    - **supported_features**: 0

## version_available Update Available {#version}
- **Entity ID**: `binary_sensor.version_available_update_available`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **device_class**: update

## WAN {#wan}
- **Entity ID**: `binary_sensor.wan`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **device_class**: connectivity

## Weather {#weather}
*2 entities*

### Weather Station Connectivity
- **Entity ID**: `binary_sensor.weather_station_connectivity` | **State**: `on`
- **Key Attributes**: device_class: connectivity
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Connectivity
- **Entity ID**: `binary_sensor.weather_station_outdoor_module_connectivity` | **State**: `unavailable`
- **Key Attributes**: device_class: connectivity
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Weather Station Ložnice Netatmo Sensor Connectivity {#weather-station-loznice-netatmo}
- **Entity ID**: `binary_sensor.weather_station_loznice_netatmo_sensor_connectivity`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **attribution**: Data provided by Netatmo
    - **device_class**: connectivity

## Weather Station Pracovna Netatmo Sensor Connectivity {#weather-station-pracovna-netatmo}
- **Entity ID**: `binary_sensor.weather_station_pracovna_netatmo_sensor_connectivity`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **attribution**: Data provided by Netatmo
    - **device_class**: connectivity

## Nightlight {#yeelight}
- **Entity ID**: `binary_sensor.yeelight_ceiling1_0x7c50757_nightlight`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Zahrada {#zahrada}
*6 entities*

### Zahrada All occupancy
- **Entity ID**: `binary_sensor.zahrada_all_occupancy` | **State**: `off`
- **Key Attributes**: device_class: occupancy

### Zahrada-Bouda All occupancy
- **Entity ID**: `binary_sensor.zahrada_bouda_all_occupancy` | **State**: `off`
- **Key Attributes**: device_class: occupancy

### Zahrada-Bouda Motion
- **Entity ID**: `binary_sensor.zahrada_bouda_motion` | **State**: `off`
- **Key Attributes**: device_class: motion

### Zahrada-Bouda Person occupancy
- **Entity ID**: `binary_sensor.zahrada_bouda_person_occupancy` | **State**: `off`
- **Key Attributes**: device_class: occupancy

### Zahrada Motion
- **Entity ID**: `binary_sensor.zahrada_motion` | **State**: `off`
- **Key Attributes**: device_class: motion

### Zahrada Person occupancy
- **Entity ID**: `binary_sensor.zahrada_person_occupancy` | **State**: `off`
- **Key Attributes**: device_class: occupancy

## Zasuvka {#zasuvka}
*2 entities*

### zasuvka-leva overcurrent
- **Entity ID**: `binary_sensor.zasuvka_leva_overcurrent` | **State**: `off`
- **Key Attributes**: device_class: problem

### zasuvka-prava overcurrent
- **Entity ID**: `binary_sensor.zasuvka_prava_overcurrent` | **State**: `off`
- **Key Attributes**: device_class: problem

## Zasuvky Technicka Mistnost {#zasuvky-technicka-mistnost}
*6 entities*

### zasuvky-technicka-mistnost switch_0 overheating
- **Entity ID**: `binary_sensor.zasuvky_technicka_mistnost_switch_0_overheating` | **State**: `off`
- **Key Attributes**: device_class: problem

### zasuvky-technicka-mistnost switch_0 overpowering
- **Entity ID**: `binary_sensor.zasuvky_technicka_mistnost_switch_0_overpowering` | **State**: `off`
- **Key Attributes**: device_class: problem

### zasuvky-technicka-mistnost switch_0 overvoltage
- **Entity ID**: `binary_sensor.zasuvky_technicka_mistnost_switch_0_overvoltage` | **State**: `off`
- **Key Attributes**: device_class: problem

### zasuvky-technicka-mistnost switch_1 overheating
- **Entity ID**: `binary_sensor.zasuvky_technicka_mistnost_switch_1_overheating` | **State**: `off`
- **Key Attributes**: device_class: problem

### zasuvky-technicka-mistnost switch_1 overpowering
- **Entity ID**: `binary_sensor.zasuvky_technicka_mistnost_switch_1_overpowering` | **State**: `off`
- **Key Attributes**: device_class: problem

### zasuvky-technicka-mistnost switch_1 overvoltage
- **Entity ID**: `binary_sensor.zasuvky_technicka_mistnost_switch_1_overvoltage` | **State**: `off`
- **Key Attributes**: device_class: problem

## Zigbee2MQTT Bridge Connection state {#zigbee2mqtt}
- **Entity ID**: `binary_sensor.zigbee2mqtt_bridge_connection_state`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:51
- **Last Updated**: 2025-06-08 19:41:51
- **Attributes**:
    - **device_class**: connectivity
