# Button Entities

[← Back to Overview](./index.md)

*Total entities: 30*

## Table of Contents

- [0X000D6Ffffefc30F1](#0x000d6ffffefc30f1)
- [Goodwe Synchronize](#goodwe-synchronize)
- [<PERSON><PERSON>](#lampa)
- [<PERSON><PERSON>](#lumi-lumi)
- [<PERSON><PERSON>](#shelly-klimatizace)
- [<PERSON><PERSON><PERSON>](#shellymini-koupelna)
- [Synchronize](#synchronize)
- [Synology](#synology)
- [Vysavac](#vysavac)
- [Vysavac Horni Patro Reset](#vysavac-horni-patro-reset)
- [Xiaomi Robot Vacuum X10](#xiaomi-robot-vacuum-x10)
- [Zasuvka](#zasuvka)
- [Zasuvky](#zasuvky)
- [Zigbee2Mqtt](#zigbee2mqtt)

## 0x000d6ffffefc30f1 Identify {#0x000d6ffffefc30f1}
- **Entity ID**: `button.0x000d6ffffefc30f1_identify`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:49
- **Last Updated**: 2025-06-08 19:41:49
- **Attributes**:
    - **device_class**: identify

## GoodWe Synchronize inverter clock {#goodwe-synchronize}
- **Entity ID**: `button.goodwe_synchronize_inverter_clock`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48

## MiDeskLampPro-171F Identify {#lampa}
- **Entity ID**: `button.lampa_xiaomi_1_identify`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **device_class**: identify

## sensor-uniku-vody-koupelna-nahore Identify {#lumi-lumi}
- **Entity ID**: `button.lumi_lumi_sensor_wleak_aq1_identify`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: identify

## shelly-klimatizace Reboot {#shelly-klimatizace}
- **Entity ID**: `button.shelly_klimatizace_reboot`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:45
- **Last Updated**: 2025-06-08 19:41:45
- **Attributes**:
    - **device_class**: restart

## Shellymini Koupelna {#shellymini-koupelna}
*2 entities*

### shellymini-koupelna-nahore-01 Reboot
- **Entity ID**: `button.shellymini_koupelna_nahore_01_reboot` | **State**: `unknown`
- **Key Attributes**: device_class: restart

### shellymini-koupelna-nahore-02 Reboot
- **Entity ID**: `button.shellymini_koupelna_nahore_02_reboot` | **State**: `unknown`
- **Key Attributes**: device_class: restart

## Google Assistant Synchronize devices {#synchronize}
- **Entity ID**: `button.synchronize_devices`
- **State**: `2023-09-15T12:58:05.042346+00:00`
- **Last Changed**: 2025-06-08 19:41:36
- **Last Updated**: 2025-06-08 19:41:36

## Synology {#synology}
*2 entities*

### Synology Reboot
- **Entity ID**: `button.synology_reboot` | **State**: `unknown`
- **Key Attributes**: device_class: restart

### Synology Shutdown
- **Entity ID**: `button.synology_shutdown` | **State**: `unknown`

## Vysavac {#vysavac}
*3 entities*

### Vysavač horní patro Reset filter
- **Entity ID**: `button.vysavac_horni_patro_reset_filter` | **State**: `2025-05-23T20:06:10.307414+00:00`
- **Key Attributes**: device_class: restart

### Vysavač horní patro Reset main brush
- **Entity ID**: `button.vysavac_horni_patro_reset_main_brush` | **State**: `unknown`
- **Key Attributes**: device_class: restart

### Vysavač horní patro Reset side brush
- **Entity ID**: `button.vysavac_horni_patro_reset_side_brush` | **State**: `unknown`
- **Key Attributes**: device_class: restart

## Vysavač horní patro Reset sensor dirty {#vysavac-horni-patro-reset}
- **Entity ID**: `button.vysavac_horni_patro_reset_sensor_dirty`
- **State**: `unknown`
- **Last Changed**: 2025-06-10 06:24:04
- **Last Updated**: 2025-06-10 06:24:04
- **Attributes**:
    - **device_class**: restart
    - **icon**: mdi:eye-outline

## Xiaomi Robot Vacuum X10 {#xiaomi-robot-vacuum-x10}
*13 entities*

### Xiaomi Robot Vacuum X10+ Clear Warning
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_clear_warning` | **State**: `unavailable`

### Xiaomi Robot Vacuum X10+ Reset Detergent
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_reset_detergent` | **State**: `unavailable`

### Xiaomi Robot Vacuum X10+ Reset Filter
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_reset_filter` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Reset Main Brush
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_reset_main_brush` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Reset Mop Pad
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_reset_mop_pad` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Reset Sensor
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_reset_sensor` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Reset Side Brush
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_reset_side_brush` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Self-Clean
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_self_clean` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Self-Clean Pause
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_self_clean_pause` | **State**: `unavailable`

### Xiaomi Robot Vacuum X10+ Start Auto Empty
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_start_auto_empty` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Start Drying
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_start_drying` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Start Fast Mapping
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_start_fast_mapping` | **State**: `unknown`

### Xiaomi Robot Vacuum X10+ Stop Drying
- **Entity ID**: `button.xiaomi_robot_vacuum_x10_stop_drying` | **State**: `unavailable`

## Zasuvka Lidl Outdoor Identify {#zasuvka}
- **Entity ID**: `button.zasuvka_lidl_outdoor_identify`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: identify

## zasuvky-technicka-mistnost Reboot {#zasuvky}
- **Entity ID**: `button.zasuvky_technicka_mistnost_reboot`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:45
- **Last Updated**: 2025-06-08 19:41:45
- **Attributes**:
    - **device_class**: restart

## Zigbee2MQTT Bridge Restart {#zigbee2mqtt}
- **Entity ID**: `button.zigbee2mqtt_bridge_restart`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:51
- **Last Updated**: 2025-06-08 19:41:51
- **Attributes**:
    - **device_class**: restart
