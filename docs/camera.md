# Camera Entities

[← Back to Overview](./index.md)

*Total entities: 11*

## Table of Contents

- [Reolin<PERSON>](#reolink)
- [Ulice](#ulice)
- [Xiaomi Robot Vacuum X10](#xiaomi-robot-vacuum-x10)
- [<PERSON><PERSON><PERSON>](#zahrada)

## Reolink zahrada Fluent {#reolink}
- **Entity ID**: `camera.reolink_zahrada_fluent`
- **State**: `idle`
- **Last Changed**: 2025-06-10 06:59:37
- **Last Updated**: 2025-06-10 07:11:35
- **Attributes**:
    - **access_token**: b7232008a2497875e1f9a401af182811eea9b1571e51b38f98b2fd5323b9fc5b
    - **entity_picture**: /api/camera_proxy/camera.reolink_zahrada_fluent?token=b7232008a2497875e1f9a401af182811eea9b1571e51b38f98b2fd5323b9fc5b
    - **frontend_stream_type**: web_rtc
    - **supported_features**: 2

## Ulice {#ulice}
*3 entities*

### ulice
- **Entity ID**: `camera.ulice` | **State**: `idle`
- **Other Attributes**:
    - **access_token**: 933c88bc6c21f4c9135e14f2ac04abba4a55e7fc70660481e784e55372c5a8d2
    - **entity_picture**: /api/camera_proxy/camera.ulice?token=933c88bc6c21f4c9135e14f2ac04abba4a55e7fc70660481e784e55372c5a8d2
    - **frontend_stream_type**: web_rtc
    - **supported_features**: 2

### Ulice
- **Entity ID**: `camera.ulice_frigate` | **State**: `recording`
- **Key Attributes**: device_class: camera
- **Other Attributes**:
    - **access_token**: 9a2332b3e6e0e0bf508fa946415df6e772a517903c192375f2721b6a52835d67
    - **camera_name**: ulice
    - **client_id**: frigate
    - **entity_picture**: /api/camera_proxy/camera.ulice_frigate?token=9a2332b3e6e0e0bf508fa946415df6e772a517903c192375f2721b6a52835d67
    - **motion_detection**: True
    - **supported_features**: 0

### Ulice
- **Entity ID**: `camera.ulice_ulice` | **State**: `recording`
- **Other Attributes**:
    - **access_token**: 193841b0018dae867994bb7c879c8bd30535a2bf5672806cd0c63f3c8af6f7b2
    - **attribution**: Data provided by Synology
    - **entity_picture**: /api/camera_proxy/camera.ulice_ulice?token=193841b0018dae867994bb7c879c8bd30535a2bf5672806cd0c63f3c8af6f7b2
    - **frontend_stream_type**: web_rtc
    - **motion_detection**: True
    - **supported_features**: 2

## Xiaomi Robot Vacuum X10 {#xiaomi-robot-vacuum-x10}
*2 entities*

### Xiaomi Robot Vacuum X10+ Current Map
- **Entity ID**: `camera.xiaomi_robot_vacuum_x10_map` | **State**: `2025-06-10 04:17:37`
- **Other Attributes**:
    - **access_token**: ef5d20d94e64d6aaf566d9f75a3ce9df439b68d7dc30983388bab9136a314a81
    - **active_segments**: [
        4
      ]
    - **calibration_points**: [
        {
          "vacuum": {
            "x": 0,
            "y": 0
          },
          "map": {
            "x": 372,
            "y": 344
          }
        },
        {
          "vacuum": {
            "x": 1000,
            "y": 0
          },
          "map": {
            "x": 452,
            "y": 344
          }
        },
        {
          "vacuum": {
            "x": 0,
            "y": 1000
          },
          "map": {
            "x": 372,
            "y": 264
          }
        }
      ]
    - **charger_position**: {
        "x": 646,
        "y": 276,
        "a": 0
      }
    - **entity_picture**: /api/camera_proxy/camera.xiaomi_robot_vacuum_x10_map?token=ef5d20d94e64d6aaf566d9f75a3ce9df439b68d7dc30983388bab9136a314a81
    - **frame_id**: 386
    - **is_empty**: False
    - **map_id**: 3
    - **rooms**: {
        "1": {
          "x0": 2500,
          "y0": 1500,
          "x1": 5350,
          "y1": 3550,
          "room_id": 1,
          "name": "Room 1",
          "order": 1,
          "cleaning_times": 1,
          "suction_level": 1,
          "water_volume": 2,
          "cleaning_mode": 2,
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 2,
          "x": 3950,
          "y": 2650,
          "letter": "A"
        },
        "2": {
          "x0": 7000,
          "y0": 150,
          "x1": 9500,
          "y1": 3350,
          "room_id": 2,
          "name": "Room 2",
          "order": 2,
          "cleaning_times": 1,
          "suction_level": 1,
          "water_volume": 2,
          "cleaning_mode": 2,
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 1,
          "x": 8000,
          "y": 1850,
          "letter": "B"
        },
        "3": {
          "x0": 2100,
          "y0": 250,
          "x1": 7200,
          "y1": 2250,
          "room_id": 3,
          "name": "Corridor",
          "order": 3,
          "cleaning_times": 1,
          "suction_level": 1,
          "water_volume": 2,
          "cleaning_mode": 2,
          "type": 8,
          "index": 0,
          "icon": "mdi:foot-print",
          "color_index": 0,
          "x": 4700,
          "y": 1300,
          "letter": "C"
        },
        "4": {
          "x0": -300,
          "y0": -3150,
          "x1": 5850,
          "y1": 3350,
          "room_id": 4,
          "name": "Living room",
          "order": 4,
          "cleaning_times": 1,
          "suction_level": 1,
          "water_volume": 2,
          "cleaning_mode": 2,
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 1,
          "unique_id": "309001003641",
          "x": 1450,
          "y": 100,
          "letter": "D"
        },
        "5": {
          "x0": 5500,
          "y0": -3500,
          "x1": 9150,
          "y1": 500,
          "room_id": 5,
          "name": "Room 5",
          "order": 5,
          "cleaning_times": 1,
          "suction_level": 1,
          "water_volume": 2,
          "cleaning_mode": 2,
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 3,
          "x": 7350,
          "y": -1800,
          "letter": "E"
        },
        "6": {
          "x0": -3900,
          "y0": -3600,
          "x1": -300,
          "y1": 2000,
          "room_id": 6,
          "name": "Room 6",
          "order": 6,
          "cleaning_times": 1,
          "suction_level": 1,
          "water_volume": 2,
          "cleaning_mode": 2,
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 0,
          "x": -1300,
          "y": -1450,
          "letter": "F"
        }
      }
    - **rotation**: 0
    - **supported_features**: 0
    - **updated_at**: 2025-06-10T04:27:49.729340
    - **used_times**: 1
    - **vacuum_position**: {
        "x": 646,
        "y": 276,
        "a": 0
      }

### Xiaomi Robot Vacuum X10+ Saved Map 1
- **Entity ID**: `camera.xiaomi_robot_vacuum_x10_map_1` | **State**: `2025-06-08 21:41:42`
- **Other Attributes**:
    - **access_token**: 668df1ae9fe3f8fbba513a216bec292f1f77fc1f7deddad566b67fcb077b8e8f
    - **calibration_points**: [
        {
          "vacuum": {
            "x": 0,
            "y": 0
          },
          "map": {
            "x": 384,
            "y": 348
          }
        },
        {
          "vacuum": {
            "x": 1000,
            "y": 0
          },
          "map": {
            "x": 464,
            "y": 348
          }
        },
        {
          "vacuum": {
            "x": 0,
            "y": 1000
          },
          "map": {
            "x": 384,
            "y": 268
          }
        }
      ]
    - **charger_position**: {
        "x": 612,
        "y": 277,
        "a": 359
      }
    - **entity_picture**: /api/camera_proxy/camera.xiaomi_robot_vacuum_x10_map_1?token=668df1ae9fe3f8fbba513a216bec292f1f77fc1f7deddad566b67fcb077b8e8f
    - **is_empty**: False
    - **map_id**: 1
    - **map_index**: 1
    - **map_name**: Map 1
    - **rooms**: {
        "1": {
          "x0": 2500,
          "y0": 1500,
          "x1": 5400,
          "y1": 3650,
          "room_id": 1,
          "name": "Room 1",
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 2,
          "x": 3950,
          "y": 2650,
          "letter": "A"
        },
        "2": {
          "x0": 6950,
          "y0": 150,
          "x1": 9500,
          "y1": 3650,
          "room_id": 2,
          "name": "Room 2",
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 1,
          "x": 8000,
          "y": 1850,
          "letter": "B"
        },
        "3": {
          "x0": 2100,
          "y0": -100,
          "x1": 7800,
          "y1": 2350,
          "room_id": 3,
          "name": "Corridor",
          "type": 8,
          "index": 0,
          "icon": "mdi:foot-print",
          "color_index": 0,
          "x": 4700,
          "y": 1300,
          "letter": "C"
        },
        "4": {
          "x0": -350,
          "y0": -3200,
          "x1": 5850,
          "y1": 3400,
          "room_id": 4,
          "name": "Living room",
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 1,
          "unique_id": "309001003641",
          "x": 1450,
          "y": 100,
          "letter": "D"
        },
        "5": {
          "x0": 5500,
          "y0": -3500,
          "x1": 9150,
          "y1": 500,
          "room_id": 5,
          "name": "Room 5",
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 3,
          "x": 7350,
          "y": -1800,
          "letter": "E"
        },
        "6": {
          "x0": -4100,
          "y0": -3650,
          "x1": -300,
          "y1": 2000,
          "room_id": 6,
          "name": "Room 6",
          "type": 0,
          "index": 0,
          "icon": "mdi:home-outline",
          "color_index": 0,
          "x": -1300,
          "y": -1450,
          "letter": "F"
        }
      }
    - **rotation**: 0
    - **supported_features**: 0
    - **updated_at**: 2025-06-09T23:49:27.237016

## Zahrada {#zahrada}
*5 entities*

### zahrada
- **Entity ID**: `camera.zahrada` | **State**: `unavailable`
- **Other Attributes**:
    - **entity_picture**: /api/camera_proxy/camera.zahrada?token=bd3811ce12529e86da151823f6e61078f6eeba8129cc6f1c2ef23d0450f6c209
    - **supported_features**: 2

### Zahrada-Bouda
- **Entity ID**: `camera.zahrada_bouda` | **State**: `recording`
- **Key Attributes**: device_class: camera
- **Other Attributes**:
    - **access_token**: ba2981f9b4dfd43d1aa599c1e7956e5ab2be0a80152d92a02706f449f2a24be6
    - **camera_name**: zahrada-bouda
    - **client_id**: frigate
    - **entity_picture**: /api/camera_proxy/camera.zahrada_bouda?token=ba2981f9b4dfd43d1aa599c1e7956e5ab2be0a80152d92a02706f449f2a24be6
    - **motion_detection**: True
    - **supported_features**: 0

### Zahrada
- **Entity ID**: `camera.zahrada_frigate` | **State**: `unavailable`
- **Key Attributes**: device_class: camera
- **Other Attributes**:
    - **entity_picture**: /api/camera_proxy/camera.zahrada_frigate?token=285e60335459bab4fedde9b0359c32bb32c4e9920a29e116fbf30cb13102f78f
    - **supported_features**: 0

### Zahrada-reo
- **Entity ID**: `camera.zahrada_reo` | **State**: `recording`
- **Other Attributes**:
    - **access_token**: b0169d117a6452b123368428060479c3c1bd8bf3f948692f3ac593d1135fd2cc
    - **attribution**: Data provided by Synology
    - **entity_picture**: /api/camera_proxy/camera.zahrada_reo?token=b0169d117a6452b123368428060479c3c1bd8bf3f948692f3ac593d1135fd2cc
    - **frontend_stream_type**: web_rtc
    - **motion_detection**: True
    - **supported_features**: 2

### Zahrada
- **Entity ID**: `camera.zahrada_zahrada` | **State**: `unavailable`
- **Other Attributes**:
    - **attribution**: Data provided by Synology
    - **entity_picture**: /api/camera_proxy/camera.zahrada_zahrada?token=ee6b3efbdbeeab78090ebfb52a7dfa1e1631a019ffd07d2e68b3f57e5726406b
    - **supported_features**: 2
