# Vacuum Entities

[← Back to Overview](./index.md)

*Total entities: 2*

## Vysavač horní patro {#vysavac}
- **Entity ID**: `vacuum.vysavac_horni_patro`
- **State**: `docked`
- **Last Changed**: 2025-06-10 06:24:04
- **Last Updated**: 2025-06-10 06:24:04
- **Attributes**:
    - **battery_icon**: mdi:battery-charging-100
    - **battery_level**: 100
    - **fan_speed**: Standard
    - **fan_speed_list**: [
        "Silent",
        "Standard",
        "Medium",
        "Turbo"
      ]
    - **status**: Charging
    - **supported_features**: 14204

## Xiaomi Robot Vacuum X10+ {#xiaomi-robot-vacuum-x10}
- **Entity ID**: `vacuum.xiaomi_robot_vacuum_x10`
- **State**: `docked`
- **Last Changed**: 2025-06-09 21:49:27
- **Last Updated**: 2025-06-10 05:24:33
- **Attributes**:
    - **active_segments**: None
    - **battery_icon**: mdi:battery-charging-100
    - **battery_level**: 100
    - **charging**: True
    - **cleaned_area**: 18
    - **cleaning_count**: 746
    - **cleaning_mode**: Sweeping
    - **cleaning_sequence**: [
        1,
        2,
        3,
        4,
        5,
        6
      ]
    - **cleaning_time**: 17
    - **current_segment**: 4
    - **customized_cleaning**: False
    - **detergent_left**: 100
    - **detergent_time_left**: 18
    - **device_class**: dreame_vacuum
    - **dnd_end**: 08:00
    - **dnd_start**: 22:00
    - **error**: No error
    - **fan_speed**: Standard
    - **fan_speed_list**: [
        "Silent",
        "Standard",
        "Strong",
        "Turbo"
      ]
    - **filter_left**: 79
    - **filter_time_left**: 119
    - **icon**: mdi:sleep
    - **main_brush_left**: 92
    - **main_brush_time_left**: 277
    - **mapping**: False
    - **mop_pad**: True
    - **mop_pad_humidity**: Wet
    - **mop_pad_humidity_list**: [
        "Slightly dry",
        "Moist",
        "Wet"
      ]
    - **mop_pad_left**: 88
    - **mop_pad_time_left**: 70
    - **paused**: False
    - **returning**: False
    - **returning_paused**: False
    - **rooms**: {
        "Map 1": [
          {
            "id": 1,
            "name": "Room 1",
            "icon": "mdi:home-outline"
          },
          {
            "id": 2,
            "name": "Room 2",
            "icon": "mdi:home-outline"
          },
          {
            "id": 3,
            "name": "Corridor",
            "icon": "mdi:foot-print"
          },
          {
            "id": 4,
            "name": "Living room",
            "icon": "mdi:home-outline"
          },
          {
            "id": 5,
            "name": "Room 5",
            "icon": "mdi:home-outline"
          },
          {
            "id": 6,
            "name": "Room 6",
            "icon": "mdi:home-outline"
          }
        ]
      }
    - **running**: False
    - **selected_map**: Map 1
    - **sensor_dirty_left**: 0
    - **sensor_dirty_time_left**: 0
    - **serial_number**: 41717/BFACWF3SY00441
    - **side_brush_left**: 43
    - **side_brush_time_left**: 87
    - **started**: False
    - **supported_features**: 15356
    - **tight_mopping**: off
    - **timezone**: Europe/Prague
    - **total_cleaned_area**: 17072
    - **total_cleaning_time**: 19432
    - **voice_packet_id**: EN
