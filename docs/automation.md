# Automation Entities

[← Back to Overview](./index.md)

*Total entities: 70*

## Table of Contents

- [Automatic](#automatic)
- [Cancel](#cancel)
- [Cast](#cast)
- [Chodba](#chodba)
- [Co2](#co2)
- [Controller](#controller)
- [Create](#create)
- [Disable](#disable)
- [Enable](#enable)
- [Finish](#finish)
- [Frigate](#frigate)
- [Hook](#hook)
- [Job](#job)
- [<PERSON><PERSON><PERSON><PERSON>](#koupelna)
- [<PERSON><PERSON>](#lampa)
- [Low](#low)
- [Loznice](#loznice)
- [Naplanovat](#naplanovat)
- [Notifikace](#notifikace)
- [Obyvaci](#obyvaci)
- [Pokoj](#pokoj)
- [Poslat](#poslat)
- [Pracka](#pracka)
- [Prepni](#prepni)
- [Sonoff Tlacitko](#sonoff-tlacitko)
- [Start](#start)
- [Susicka](#susicka)
- [Svetla](#svetla)
- [Sync](#sync)
- [Tag](#tag)
- [Topeni](#topeni)
- [Turn](#turn)
- [Update](#update)
- [Vypnout](#vypnout)
- [Water](#water)
- [Zapnout](#zapnout)
- [Zigbee](#zigbee)

## Automatic Backups {#automatic}
- **Entity ID**: `automation.automatic_backups`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1689107650086
    - **last_triggered**: 2025-03-06T00:00:00.271785+00:00
    - **mode**: single

## Cancel {#cancel}
*2 entities*

### Cancel pohyb_bouda
- **Entity ID**: `automation.cancel_pohyb_bouda` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: cancel pohyb_bouda
    - **last_triggered**: 2023-01-16T19:37:09.021088+00:00
    - **mode**: single

### Cancel pohyb_zahrada
- **Entity ID**: `automation.cancel_pohyb_zahrada` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: cancel pohyb_zahrada
    - **last_triggered**: 2023-04-16T12:34:33.021026+00:00
    - **mode**: single

## cast_to_google_living_room {#cast}
- **Entity ID**: `automation.cast_to_google_living_room`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: cast_to_google_living_room
    - **last_triggered**: 2019-12-07T19:44:43.798342+00:00
    - **mode**: single

## Chodba mode setup {#chodba}
- **Entity ID**: `automation.chodba_mode_setup`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: ChodbaModeSetup
    - **last_triggered**: 2021-12-18T08:03:35.331037+00:00
    - **mode**: single

## co2_tts_obyvak {#co2}
- **Entity ID**: `automation.co2_tts_obyvak`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: co2_tts_obyvak
    - **last_triggered**: 2025-06-07T20:55:23.235515+00:00
    - **mode**: single

## Controller - Philips 324131092621 Hue Dimmer switch {#controller}
- **Entity ID**: `automation.controller_philips_324131092621_hue_dimmer_switch`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1696527432938
    - **last_triggered**: 2025-01-02T05:18:19.508868+00:00
    - **mode**: restart

## Create a notification when there is updates pending in HACS {#create}
- **Entity ID**: `automation.create_a_notification_when_there_is_updates_pending_in_hacs`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: notify-hacs
    - **last_triggered**: 2024-09-10T13:11:01.039017+00:00
    - **mode**: single

## Disable {#disable}
*2 entities*

### Disable Zigbee joining
- **Entity ID**: `automation.disable_zigbee_joining` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: disable_zigbee_join
    - **last_triggered**: 2023-02-03T19:43:25.036556+00:00
    - **mode**: single

### Disable Zigbee joining by timer
- **Entity ID**: `automation.disable_zigbee_joining_by_timer` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: disable_zigbee_join_timer
    - **last_triggered**: 2023-02-03T19:43:25.014806+00:00
    - **mode**: single

## Enable {#enable}
*2 entities*

### Enable MQTT discovery for all devices
- **Entity ID**: `automation.enable_mqtt_discovery_for_all_devices` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Enable Zigbee joining
- **Entity ID**: `automation.enable_zigbee_joining` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: enable_zigbee_join
    - **last_triggered**: 2023-02-03T19:33:24.974456+00:00
    - **mode**: single

## Finish {#finish}
*2 entities*

### Finish of pohyb_bouda
- **Entity ID**: `automation.finish_of_pohyb_bouda` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: finish of pohyb_bouda
    - **last_triggered**: 2023-01-16T19:37:09.005032+00:00
    - **mode**: single

### Finish of pohyb_zahrada
- **Entity ID**: `automation.finish_of_pohyb_zahrada` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: finish of pohyb_zahrada
    - **last_triggered**: 2023-04-16T12:34:33.004483+00:00
    - **mode**: single

## Frigate Notifications (********) {#frigate}
- **Entity ID**: `automation.frigate_notifications_0_12_0_1`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1693859224888
    - **last_triggered**: 2023-09-07T11:14:21.797331+00:00
    - **max**: 10
    - **mode**: parallel

## Hook - Light - philips strip {#hook}
- **Entity ID**: `automation.hook_light_philips_strip`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1681906141281
    - **last_triggered**: 2024-12-08T12:19:22.177583+00:00
    - **mode**: restart

## Job {#job}
*2 entities*

### Job:Mam vypnout topeni?
- **Entity ID**: `automation.job_mam_vypnout_topeni` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: mam-vypnout-topeni
    - **last_triggered**: 2025-06-09T19:40:00.187083+00:00
    - **mode**: single

### Job:Mam zapnout topeni?
- **Entity ID**: `automation.job_mam_zapnout_topeni` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: mam-zapnout-topeni
    - **last_triggered**: 2025-05-31T04:45:00.458585+00:00
    - **mode**: single

## Koupelna {#koupelna}
*2 entities*

### Koupelna dole mode setup
- **Entity ID**: `automation.koupelna_dole_mode_setup` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: Koupelnadolemodesetup
    - **last_triggered**: 2025-06-08T20:11:54.146950+00:00
    - **mode**: single

### Koupelna nahore mode setup
- **Entity ID**: `automation.koupelna_nahore_mode_setup` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: KoupelnaNahoreModeSetup
    - **last_triggered**: 2024-08-14T20:02:35.141853+00:00
    - **mode**: single

## Lampa {#lampa}
*5 entities*

### Lampa v ložnici - vypnout
- **Entity ID**: `automation.lampa_v_loznici_vypnout` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: lampa_loznice_vypnout
    - **last_triggered**: 2024-12-08T12:19:22.159091+00:00
    - **mode**: single

### Lampa v ložnici - zapnout
- **Entity ID**: `automation.lampa_v_loznici_zapnout` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: lampa_loznice_zapnout
    - **last_triggered**: 2024-11-22T18:52:08.814918+00:00
    - **mode**: single

### Lampa v ložnici - zesílit
- **Entity ID**: `automation.lampa_v_loznici_zesilit` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: lampa_loznice_zesilit
    - **last_triggered**: 2020-08-04T20:38:03.107189+00:00
    - **mode**: single

### Lampa v ložnici - zesílit z vypnuto
- **Entity ID**: `automation.lampa_v_loznici_zesilit_z_vypnuto` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: lampa_loznice_zesilit_vypnuto
    - **last_triggered**: 2020-08-26T19:36:45.532226+00:00
    - **mode**: single

### Lampa v ložnici - zeslabit
- **Entity ID**: `automation.lampa_v_loznici_zeslabit` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: lampa_loznice_zeslabit
    - **last_triggered**: 2024-11-20T15:34:23.955849+00:00
    - **mode**: single

## Low battery lnotifikace nizky srtav baterie {#low}
- **Entity ID**: `automation.low_battery_lnotifikace_nizky_srtav_baterie`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-09 08:00:07
- **Attributes**:
    - **current**: 0
    - **id**: 1681897102005
    - **last_triggered**: 2025-06-09T08:00:00.466465+00:00
    - **mode**: single

## Loznice mode setup {#loznice}
- **Entity ID**: `automation.loznice_mode_setup`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-09 17:50:22
- **Attributes**:
    - **current**: 0
    - **id**: LozniceModeSetup
    - **last_triggered**: 2025-06-09T17:50:22.062068+00:00
    - **mode**: single

## Naplanovat {#naplanovat}
*2 entities*

### Naplánovat vysávání o víkendu?
- **Entity ID**: `automation.naplanovat_vysavani_o_vikendu` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Naplánovat vysávání v týdnu?
- **Entity ID**: `automation.naplanovat_vysavani_v_tydnu` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Notifikace když je kotel offline {#notifikace}
- **Entity ID**: `automation.notifikace_kdyz_je_kotel_offline`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: Notifikacekdyejkoteloffline
    - **last_triggered**: 2025-03-27T13:14:07.763976+00:00
    - **mode**: single

## Obyvaci pokoj mode setup {#obyvaci}
- **Entity ID**: `automation.obyvaci_pokoj_mode_setup`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-09 19:02:40
- **Attributes**:
    - **current**: 0
    - **id**: Obyvacipokojmodesetup
    - **last_triggered**: 2025-06-09T19:02:40.392248+00:00
    - **mode**: single

## Pokoj {#pokoj}
*3 entities*

### Pokoj dole mode setup
- **Entity ID**: `automation.pokoj_dole_mode_setup` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: Pokojdolemodesetup
    - **last_triggered**: 2025-05-09T20:15:50.575654+00:00
    - **mode**: single

### Pokoj levy mode setup
- **Entity ID**: `automation.pokoj_levy_mode_setup` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: PokojLevyModeSetup
    - **last_triggered**: 2025-03-23T13:20:31.337754+00:00
    - **mode**: single

### Pokoj pravy mode setup
- **Entity ID**: `automation.pokoj_pravy_mode_setup` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: pokoj-pravy-mode-setup
    - **last_triggered**: 2025-01-12T15:42:42.649036+00:00
    - **mode**: single

## Poslat snimek zahrady {#poslat}
- **Entity ID**: `automation.poslat_snimek_zahrady`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **last_triggered**: 2020-02-04T02:07:39.304617+00:00
    - **mode**: single

## Pračka doprala {#pracka}
- **Entity ID**: `automation.pracka_doprala`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1699008192984
    - **last_triggered**: 2025-06-07T09:16:03.878862+00:00
    - **mode**: single

## Prepni {#prepni}
*2 entities*

### Prepni Ventilator
- **Entity ID**: `automation.prepni_ventilator` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: 1681649834647
    - **last_triggered**: 2023-12-22T15:27:37.999439+00:00
    - **mode**: single

### Přepni zásuvku v Adunce pokoji
- **Entity ID**: `automation.prepni_zasuvku_v_adunce_pokoji` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: prepni-zasuvku-adelka
    - **last_triggered**: 2024-01-03T17:34:03.429600+00:00
    - **mode**: single

## Sonoff-tlacitko-prepni-filtraci-bazen {#sonoff-tlacitko}
- **Entity ID**: `automation.sonoff_tlacitko_prepni_filtraci_bazen`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1718887174450
    - **last_triggered**: 2025-05-18T15:57:59.666326+00:00
    - **mode**: single

## Start {#start}
*2 entities*

### Start pohyb_bouda
- **Entity ID**: `automation.start_pohyb_bouda` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: start pohyb_bouda
    - **last_triggered**: 2023-01-16T19:36:09.800755+00:00
    - **mode**: single

### Start pohyb_zahrada
- **Entity ID**: `automation.start_pohyb_zahrada` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: start pohyb_zahrada
    - **last_triggered**: 2023-04-16T12:31:33.432845+00:00
    - **mode**: single

## Susička dosušila {#susicka}
- **Entity ID**: `automation.susicka_dosusila`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1699009489947
    - **last_triggered**: 2025-06-07T10:33:19.669102+00:00
    - **mode**: single

## Svetla {#svetla}
*4 entities*

### Světla v pracovně rozsvítit
- **Entity ID**: `automation.svetla_v_pracovne_rozsvitit` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: 1640725602682
    - **last_triggered**: 2023-08-04T16:59:00.731472+00:00
    - **mode**: single

### Svetla v pracovne zhasnout po 10 minutach
- **Entity ID**: `automation.svetla_v_pracovne_zhasnout_po_10_minutach` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: sverla_v_pracovne_zhasnout_po_10_minutach
    - **last_triggered**: 2024-12-17T12:57:07.249808+00:00
    - **mode**: single

### Svetla v pracovne zhasnout po 120 sekundach
- **Entity ID**: `automation.svetla_v_pracovne_zhasnout_po_120_sekundach` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: sverla_v_pracovne_zhasnout_po_120_sekundach
    - **last_triggered**: 2024-12-17T12:49:07.248389+00:00
    - **mode**: single

### Svetla v pracovne zhasnout po 5 minutach
- **Entity ID**: `automation.svetla_v_pracovne_zhasnout_po_5_minutach` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: sverla_v_pracovne_zhasnout_po_5_minutach
    - **last_triggered**: 2024-12-17T12:52:07.249469+00:00
    - **mode**: single

## Sync {#sync}
*2 entities*

### Sync temperature from klimatizace_pracovna to pokoj_pravy
- **Entity ID**: `automation.sync_temperature_from_klimatizace_pracovna_to_pokoj_pravy` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: syncklimatizace_pracovnaclima
    - **last_triggered**: 2024-10-31T12:38:27.975545+00:00
    - **mode**: single

### Sync temperature from pokoj_pravy to klimatizace_pracovna
- **Entity ID**: `automation.sync_temperature_from_pokoj_pravy_to_klimatizace_pracovna` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: syncpokoj_pravyclima
    - **last_triggered**: 2024-10-31T12:38:27.481098+00:00
    - **mode**: single

## Tag zelený pravý pokoj scanned {#tag}
- **Entity ID**: `automation.tag_zeleny_pravy_pokoj_scanned`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: 1718871647261
    - **last_triggered**: 2024-07-17T10:02:48.291699+00:00
    - **mode**: single

## Topení v pracovně vypnout po 45 minutach necinnosti {#topeni}
- **Entity ID**: `automation.topeni_v_pracovne_vypnout_po_45_minutach_necinnosti`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-09 20:32:54
- **Attributes**:
    - **current**: 0
    - **id**: topeni_pracovna_vypnout
    - **last_triggered**: 2025-06-09T20:32:54.023428+00:00
    - **mode**: single

## Turn {#turn}
*2 entities*

### Turn off chodba light 5 seconds after last movement
- **Entity ID**: `automation.turn_off_chodba_light_5_seconds_after_last_movement` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: vypnout_chodba_po_pohybu
    - **last_triggered**: 2025-06-10T06:38:52.706446+00:00
    - **mode**: single

### Turn on chodba on movenment
- **Entity ID**: `automation.turn_on_chodba_on_movenment` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: zapnout_chodba_pohyb
    - **last_triggered**: 2025-06-10T06:37:17.697831+00:00
    - **mode**: single

## Update notifications {#update}
- **Entity ID**: `automation.update_notifications`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: update_notify
    - **last_triggered**: 2019-08-29T21:31:57.486486+00:00
    - **mode**: single

## Vypnout {#vypnout}
*8 entities*

### Vypnout konvici po 5 minutach
- **Entity ID**: `automation.vypnout_konvici_po_5_minutach` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: vypnout_konvici
    - **last_triggered**: 2025-06-09T07:18:05.402031+00:00
    - **mode**: single

### Vypnout OpenMediaVault kdyz neni odber
- **Entity ID**: `automation.vypnout_openmediavault_kdyz_neni_odber` | **State**: `unavailable`
- **Other Attributes**:
    - **id**: vypnoutopenmediavaultkdyzneniodber
    - **restored**: True
    - **supported_features**: 0

### Vypnout topeni rano v zadany cas
- **Entity ID**: `automation.vypnout_topeni_rano_v_zadany_cas` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: Vypnouttopeniranovzadanycas
    - **last_triggered**: 2025-06-04T17:45:00.274670+00:00
    - **mode**: single

### Vypnout topeni v dome na noc
- **Entity ID**: `automation.vypnout_topeni_v_dome_na_noc` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: vypnout_topeni_noc
    - **last_triggered**: 2025-06-04T20:00:00.491506+00:00
    - **mode**: single

### Vypnout topeni v koupelnach
- **Entity ID**: `automation.vypnout_topeni_v_koupelnach` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: vypnout_topeni_koupelny
    - **last_triggered**: 2025-06-09T18:00:00.117962+00:00
    - **mode**: single

### Vypnout ventilator v pracovne po 10 minutach
- **Entity ID**: `automation.vypnout_ventilator_v_pracovne_po_10_minutach` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: vypnout_ventilator_pracovna_po_10_minutach
    - **last_triggered**: 2023-07-10T16:11:02.949908+00:00
    - **mode**: single

### Vypnout zarovky oyvak
- **Entity ID**: `automation.vypnout_zarovky_oyvak` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: 1733064108826
    - **last_triggered**: 2025-01-17T21:30:00.229552+00:00
    - **mode**: single

### Vypnout zasuvky zahrada lidl outdoor
- **Entity ID**: `automation.vypnout_zasuvky_zahrada_lidl_outdoor` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: 1733654856292
    - **last_triggered**: 2025-01-13T21:30:00.426775+00:00
    - **mode**: single

## Water Detected iOS {#water}
- **Entity ID**: `automation.water_detected_ios`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **last_triggered**: 2023-12-11T13:21:05.275169+00:00
    - **mode**: single

## Zapnout {#zapnout}
*7 entities*

### Zapnout Openmediavault v zadany cas
- **Entity ID**: `automation.zapnout_openmediavault_v_zadany_cas` | **State**: `unavailable`
- **Other Attributes**:
    - **id**: zapnoutopenmediavaultvzadanycas
    - **restored**: True
    - **supported_features**: 0

### Zapnout topeni koupelna dole v zadany cas
- **Entity ID**: `automation.zapnout_topeni_koupelna_dole_v_zadany_cas` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: zapnout_topeni_koupelna_dole
    - **last_triggered**: 2022-01-16T16:20:00.015602+00:00
    - **mode**: single

### Zapnout topeni koupelna nahore v zadany cas
- **Entity ID**: `automation.zapnout_topeni_koupelna_nahore_v_zadany_cas` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: zapnout_topeni_koupelna_nahore
    - **last_triggered**: 2023-01-25T15:00:00.449886+00:00
    - **mode**: single

### Zapnout topeni pokoj levy v zadany cas v pracovni dny
- **Entity ID**: `automation.zapnout_topeni_pokoj_levy_v_zadany_cas_v_pracovni_dny` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: zapnout_topeni_pokoj_levy_pracovni_dny
    - **last_triggered**: 2025-06-05T10:15:00.170307+00:00
    - **mode**: single

### Zapnout topeni rano v zadany cas
- **Entity ID**: `automation.zapnout_topeni_rano_v_zadany_cas` | **State**: `on`
- **Other Attributes**:
    - **current**: 0
    - **id**: Zapnouttopeniranovzadanycas
    - **last_triggered**: 2025-06-05T04:00:00.196760+00:00
    - **mode**: single

### Zapnout zarovky obyvak
- **Entity ID**: `automation.zapnout_zarovky_obyvak` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: 1733063996460
    - **last_triggered**: 2025-01-18T14:30:00.255033+00:00
    - **mode**: single

### Zapnout zasuvky venku zahrada lidl outdoor
- **Entity ID**: `automation.zapnout_zasuvky_venku_zahrada_lidl_outdoor` | **State**: `off`
- **Other Attributes**:
    - **current**: 0
    - **id**: 1733654807368
    - **last_triggered**: 2025-01-13T14:30:00.159136+00:00
    - **mode**: single

## Zigbee Map aktualizace {#zigbee}
- **Entity ID**: `automation.zigbee_map_aktualizace`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **current**: 0
    - **id**: update_networkmap
    - **last_triggered**: 2021-12-28T21:00:00.012236+00:00
    - **mode**: single
