# Number Entities

[← Back to Overview](./index.md)

*Total entities: 28*

## Table of Contents

- [0X00158D0002B8C8E7](#0x00158d0002b8c8e7)
- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [Chlad<PERSON><PERSON>](#chladnicka)
- [<PERSON>we Depth](#goodwe-depth)
- [Goodwe Grid](#goodwe-grid)
- [Klimatizace Pokoj](#klimatizace-pokoj)
- [Reolink](#reolink)
- [Xiaomi Robot Vacuum X10](#xiaomi-robot-vacuum-x10)

## 0x00158d0002b8c8e7 Sensitivity {#0x00158d0002b8c8e7}
- **Entity ID**: `number.0x00158d0002b8c8e7_sensitivity`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:49
- **Last Updated**: 2025-06-08 19:41:49
- **Attributes**:
    - **max**: 21.0
    - **min**: 1.0
    - **mode**: auto
    - **step**: 1.0

## 0X84Fd27Fffea36224 {#0x84fd27fffea36224}
*6 entities*

### 0x84fd27fffea36224 Boost timeset countdown
- **Entity ID**: `number.0x84fd27fffea36224_boost_timeset_countdown` | **State**: `0`
- **Key Attributes**: unit_of_measurement: s
- **Other Attributes**:
    - **max**: 465.0
    - **min**: 0.0
    - **mode**: auto
    - **step**: 1.0

### 0x84fd27fffea36224 Comfort temperature
- **Entity ID**: `number.0x84fd27fffea36224_comfort_temperature` | **State**: `21`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **max**: 30.0
    - **min**: 5.0
    - **mode**: auto
    - **step**: 1.0

### 0x84fd27fffea36224 Eco temperature
- **Entity ID**: `number.0x84fd27fffea36224_eco_temperature` | **State**: `17`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **max**: 30.0
    - **min**: 5.0
    - **mode**: auto
    - **step**: 1.0

### 0x84fd27fffea36224 Holiday temperature
- **Entity ID**: `number.0x84fd27fffea36224_holiday_temperature` | **State**: `19`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **max**: 30.0
    - **min**: 5.0
    - **mode**: auto
    - **step**: 1.0

### 0x84fd27fffea36224 Temperature
- **Entity ID**: `number.0x84fd27fffea36224_local_temperature_calibration` | **State**: `-0.4`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C
- **Other Attributes**:
    - **max**: 5.0
    - **min**: -5.0
    - **mode**: auto
    - **step**: 0.1

### 0x84fd27fffea36224 Open window temperature
- **Entity ID**: `number.0x84fd27fffea36224_open_window_temperature` | **State**: `13`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **max**: 30.0
    - **min**: 5.0
    - **mode**: auto
    - **step**: 1.0

## Chladnicka {#chladnicka}
*2 entities*

### Chladnička freezer temperature
- **Entity ID**: `number.chladnicka_freezer_temperature` | **State**: `-21`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **max**: -15
    - **min**: -23
    - **mode**: box
    - **step**: 1

### Chladnička fridge temperature
- **Entity ID**: `number.chladnicka_fridge_temperature` | **State**: `5`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **max**: 7
    - **min**: 1
    - **mode**: box
    - **step**: 1

## GoodWe Depth of discharge (on-grid) {#goodwe-depth}
- **Entity ID**: `number.goodwe_depth_of_discharge_on_grid`
- **State**: `85.0`
- **Last Changed**: 2025-06-08 19:41:49
- **Last Updated**: 2025-06-08 19:41:49
- **Attributes**:
    - **icon**: mdi:battery-arrow-down
    - **max**: 99
    - **min**: 0
    - **mode**: auto
    - **step**: 1
    - **unit_of_measurement**: %

## GoodWe Grid export limit {#goodwe-grid}
- **Entity ID**: `number.goodwe_grid_export_limit`
- **State**: `0.0`
- **Last Changed**: 2025-06-08 19:41:49
- **Last Updated**: 2025-06-08 19:41:49
- **Attributes**:
    - **device_class**: power
    - **max**: 10000
    - **min**: 0
    - **mode**: auto
    - **step**: 100
    - **unit_of_measurement**: W

## Klimatizace Pokoj {#klimatizace-pokoj}
*9 entities*

### Klimatizace pokoj levý Schedule turn-off
- **Entity ID**: `number.klimatizace_pokoj_levy_schedule_turn_off` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj levý Schedule turn-on
- **Entity ID**: `number.klimatizace_pokoj_levy_schedule_turn_on` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj levý Sleep timer
- **Entity ID**: `number.klimatizace_pokoj_levy_sleep_timer` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj pravý Schedule turn-off
- **Entity ID**: `number.klimatizace_pokoj_pravy_schedule_turn_off` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj pravý Schedule turn-on
- **Entity ID**: `number.klimatizace_pokoj_pravy_schedule_turn_on` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj pravý Sleep timer
- **Entity ID**: `number.klimatizace_pokoj_pravy_sleep_timer` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj stred Schedule turn-off
- **Entity ID**: `number.klimatizace_pokoj_stred_schedule_turn_off` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj stred Schedule turn-on
- **Entity ID**: `number.klimatizace_pokoj_stred_schedule_turn_on` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

### Klimatizace pokoj stred Sleep timer
- **Entity ID**: `number.klimatizace_pokoj_stred_sleep_timer` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: h
- **Other Attributes**:
    - **max**: 100.0
    - **min**: 0.0
    - **mode**: box
    - **step**: 1.0

## Reolink {#reolink}
*6 entities*

### Reolink zahrada AI animal sensitivity
- **Entity ID**: `number.reolink_zahrada_ai_animal_sensitivity` | **State**: `76`
- **Other Attributes**:
    - **max**: 100
    - **min**: 0
    - **mode**: auto
    - **step**: 1

### Reolink zahrada AI person sensitivity
- **Entity ID**: `number.reolink_zahrada_ai_person_sensitivity` | **State**: `77`
- **Other Attributes**:
    - **max**: 100
    - **min**: 0
    - **mode**: auto
    - **step**: 1

### Reolink zahrada AI vehicle sensitivity
- **Entity ID**: `number.reolink_zahrada_ai_vehicle_sensitivity` | **State**: `60`
- **Other Attributes**:
    - **max**: 100
    - **min**: 0
    - **mode**: auto
    - **step**: 1

### Reolink zahrada Floodlight turn on brightness
- **Entity ID**: `number.reolink_zahrada_floodlight_turn_on_brightness` | **State**: `85`
- **Other Attributes**:
    - **max**: 100
    - **min**: 1
    - **mode**: auto
    - **step**: 1

### Reolink zahrada Motion sensitivity
- **Entity ID**: `number.reolink_zahrada_motion_sensitivity` | **State**: `42`
- **Other Attributes**:
    - **max**: 50
    - **min**: 1
    - **mode**: auto
    - **step**: 1

### Reolink zahrada Volume
- **Entity ID**: `number.reolink_zahrada_volume` | **State**: `92`
- **Other Attributes**:
    - **max**: 100
    - **min**: 0
    - **mode**: auto
    - **step**: 1

## Xiaomi Robot Vacuum X10 {#xiaomi-robot-vacuum-x10}
*2 entities*

### Xiaomi Robot Vacuum X10+ Mop Cleaning Remainder
- **Entity ID**: `number.xiaomi_robot_vacuum_x10_mop_cleaning_remainder` | **State**: `0`
- **Key Attributes**: unit_of_measurement: min
- **Other Attributes**:
    - **max**: 180
    - **min**: 0
    - **mode**: box
    - **step**: 15

### Xiaomi Robot Vacuum X10+ Volume
- **Entity ID**: `number.xiaomi_robot_vacuum_x10_volume` | **State**: `90`
- **Other Attributes**:
    - **max**: 100
    - **min**: 0
    - **mode**: slider
    - **step**: 1
