# Sensor Entities

[← Back to Overview](./index.md)

*Total entities: 646*

## Table of Contents

- [0X00124B001938Efef](#0x00124b001938efef)
- [0X00124B0024553222](#0x00124b0024553222)
- [0X00158D0001E7Daf6](#0x00158d0001e7daf6)
- [0X00158D00020B5Abb](#0x00158d00020b5abb)
- [0X00158D00023E3E49](#0x00158d00023e3e49)
- [0X00158D0002B8C8E7](#0x00158d0002b8c8e7)
- [0X00158D0002D7976F](#0x00158d0002d7976f)
- [0X00158D0002E214D4](#0x00158d0002e214d4)
- [0X00158D0002E2697E](#0x00158d0002e2697e)
- [0X00158D0002Ea1003](#0x00158d0002ea1003)
- [0X00158D00030423C7](#0x00158d00030423c7)
- [0X00158D0003140D60](#0x00158d0003140d60)
- [0X00158D000315C387](#0x00158d000315c387)
- [0X00158D000315Fac9](#0x00158d000315fac9)
- [0X00158D000322B60F](#0x00158d000322b60f)
- [0X0017880104F1247D](#0x0017880104f1247d)
- [0X0C4314Fffe5Fe450](#0x0c4314fffe5fe450)
- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [0Xa4C13887Ab4A50C0](#0xa4c13887ab4a50c0)
- [Active](#active)
- [Aktualni](#aktualni)
- [Back](#back)
- [Backup](#backup)
- [Battery](#battery)
- [Bazenove](#bazenove)
- [Bridge](#bridge)
- [Bus](#bus)
- [Cert](#cert)
- [Cesta](#cesta)
- [Chladnicka](#chladnicka)
- [Commode](#commode)
- [Current](#current)
- [Dest](#dest)
- [Diag](#diag)
- [Disk](#disk)
- [Display](#display)
- [Doba](#doba)
- [Energy](#energy)
- [Error](#error)
- [Errors](#errors)
- [Function](#function)
- [Grid](#grid)
- [Gw1100A](#gw1100a)
- [House](#house)
- [Hp](#hp)
- [Illumination](#illumination)
- [Inverter](#inverter)
- [Iphone](#iphone)
- [Java](#java)
- [Klimatizace Pokoj](#klimatizace-pokoj)
- [Klimatizace Pracovna](#klimatizace-pracovna)
- [Kotel](#kotel)
- [Last](#last)
- [Load](#load)
- [Lora](#lora)
- [Lumi Lumi](#lumi-lumi)
- [Manufacture](#manufacture)
- [Memory](#memory)
- [Meter](#meter)
- [My](#my)
- [My Tp](#my-tp)
- [Nbus](#nbus)
- [Netatmo Moje](#netatmo-moje)
- [On](#on)
- [Openweathermap](#openweathermap)
- [Operation](#operation)
- [Pavel](#pavel)
- [Pavels](#pavels)
- [Pi](#pi)
- [Pocitova](#pocitova)
- [Power](#power)
- [Processor](#processor)
- [Pv](#pv)
- [Pv1](#pv1)
- [Pv2](#pv2)
- [Reolink](#reolink)
- [Req](#req)
- [Rssi](#rssi)
- [Safety](#safety)
- [Shelly Klimatizace](#shelly-klimatizace)
- [Shellymini Koupelna](#shellymini-koupelna)
- [Sonoff 4Ch01](#sonoff-4ch01)
- [Sonoff Energy](#sonoff-energy)
- [Sonoff Last](#sonoff-last)
- [Sonoff Mqtt](#sonoff-mqtt)
- [Sonoff Restart](#sonoff-restart)
- [Sonoff Ssid](#sonoff-ssid)
- [Sonoff Wifi](#sonoff-wifi)
- [Sonoffpow02](#sonoffpow02)
- [Sun](#sun)
- [Synology](#synology)
- [Tb350Fu](#tb350fu)
- [Teplota](#teplota)
- [Timestamp](#timestamp)
- [Today](#today)
- [Total](#total)
- [Ulice](#ulice)
- [Ups](#ups)
- [Venkovni](#venkovni)
- [Version](#version)
- [Vlhkost](#vlhkost)
- [Vlhkostni](#vlhkostni)
- [Vnitrni](#vnitrni)
- [Vysavac](#vysavac)
- [Vysavac Horni Patro](#vysavac-horni-patro)
- [Warning](#warning)
- [Weather Station Weather Station Weather Station Loznice Netatmo](#weather-station-weather-station-weather-station-loznice-netatmo)
- [Weather Station Weather Station Weather Station Pracovna Netatmo](#weather-station-weather-station-weather-station-pracovna-netatmo)
- [Wifi](#wifi)
- [Wifi Signal](#wifi-signal)
- [Work](#work)
- [Xiaomi Robot Vacuum X10](#xiaomi-robot-vacuum-x10)
- [Zahrada](#zahrada)
- [Zasuvka](#zasuvka)
- [Zasuvky Technicka Mistnost](#zasuvky-technicka-mistnost)
- [Zigbee2Mqtt](#zigbee2mqtt)

## sensor.0x00124b001938efef_linkquality {#0x00124b001938efef}
- **Entity ID**: `sensor.0x00124b001938efef_linkquality`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## 0x00124b0024553222 Battery {#0x00124b0024553222}
- **Entity ID**: `sensor.0x00124b0024553222_battery`
- **State**: `100`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **device_class**: battery
    - **state_class**: measurement
    - **unit_of_measurement**: %

## 0X00158D0001E7Daf6 {#0x00158d0001e7daf6}
*3 entities*

### 0x00158d0001e7daf6 Battery
- **Entity ID**: `sensor.0x00158d0001e7daf6_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d0001e7daf6 Temperature
- **Entity ID**: `sensor.0x00158d0001e7daf6_device_temperature` | **State**: `24`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### 0x00158d0001e7daf6 Power outage count
- **Entity ID**: `sensor.0x00158d0001e7daf6_power_outage_count` | **State**: `285`

## 0X00158D00020B5Abb {#0x00158d00020b5abb}
*2 entities*

### 0x00158d00020b5abb Battery
- **Entity ID**: `sensor.0x00158d00020b5abb_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d00020b5abb Power outage count
- **Entity ID**: `sensor.0x00158d00020b5abb_power_outage_count` | **State**: `472`

## 0X00158D00023E3E49 {#0x00158d00023e3e49}
*3 entities*

### 0x00158d00023e3e49 Battery
- **Entity ID**: `sensor.0x00158d00023e3e49_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d00023e3e49 Temperature
- **Entity ID**: `sensor.0x00158d00023e3e49_device_temperature` | **State**: `25`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### 0x00158d00023e3e49 Power outage count
- **Entity ID**: `sensor.0x00158d00023e3e49_power_outage_count` | **State**: `40`

## 0X00158D0002B8C8E7 {#0x00158d0002b8c8e7}
*18 entities*

### 0x00158d0002b8c8e7 action
- **Entity ID**: `sensor.0x00158d0002b8c8e7_action` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 angle x
- **Entity ID**: `sensor.0x00158d0002b8c8e7_angle_x` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Angle x
- **Entity ID**: `sensor.0x00158d0002b8c8e7_angle_x_2` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °

### 0x00158d0002b8c8e7 angle y
- **Entity ID**: `sensor.0x00158d0002b8c8e7_angle_y` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Angle y
- **Entity ID**: `sensor.0x00158d0002b8c8e7_angle_y_2` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °

### 0x00158d0002b8c8e7 angle z
- **Entity ID**: `sensor.0x00158d0002b8c8e7_angle_z` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Angle z
- **Entity ID**: `sensor.0x00158d0002b8c8e7_angle_z_2` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °

### 0x00158d0002b8c8e7 battery
- **Entity ID**: `sensor.0x00158d0002b8c8e7_battery` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Battery
- **Entity ID**: `sensor.0x00158d0002b8c8e7_battery_2` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d0002b8c8e7 device temperature
- **Entity ID**: `sensor.0x00158d0002b8c8e7_device_temperature` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Temperature
- **Entity ID**: `sensor.0x00158d0002b8c8e7_device_temperature_2` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### 0x00158d0002b8c8e7 power outage count
- **Entity ID**: `sensor.0x00158d0002b8c8e7_power_outage_count` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Power outage count
- **Entity ID**: `sensor.0x00158d0002b8c8e7_power_outage_count_2` | **State**: `unavailable`

### 0x00158d0002b8c8e7 strength
- **Entity ID**: `sensor.0x00158d0002b8c8e7_strength` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002b8c8e7 Strength
- **Entity ID**: `sensor.0x00158d0002b8c8e7_strength_2` | **State**: `unavailable`

### 0x00158d0002b8c8e7 X axis
- **Entity ID**: `sensor.0x00158d0002b8c8e7_x_axis` | **State**: `unavailable`

### 0x00158d0002b8c8e7 Y axis
- **Entity ID**: `sensor.0x00158d0002b8c8e7_y_axis` | **State**: `unavailable`

### 0x00158d0002b8c8e7 Z axis
- **Entity ID**: `sensor.0x00158d0002b8c8e7_z_axis` | **State**: `unavailable`

## 0X00158D0002D7976F {#0x00158d0002d7976f}
*4 entities*

### Teplotní čidlo technická místnost battery
- **Entity ID**: `sensor.0x00158d0002d7976f_battery` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### Teplotní čidlo technická místnost humidity
- **Entity ID**: `sensor.0x00158d0002d7976f_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

### Teplotní čidlo technická místnost pressure
- **Entity ID**: `sensor.0x00158d0002d7976f_pressure` | **State**: `unavailable`
- **Key Attributes**: device_class: atmospheric_pressure, unit_of_measurement: hPa, state_class: measurement

### Teplotní čidlo technická místnost temperature
- **Entity ID**: `sensor.0x00158d0002d7976f_temperature` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## 0X00158D0002E214D4 {#0x00158d0002e214d4}
*3 entities*

### 0x00158d0002e214d4 Battery
- **Entity ID**: `sensor.0x00158d0002e214d4_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d0002e214d4 Temperature
- **Entity ID**: `sensor.0x00158d0002e214d4_device_temperature` | **State**: `24`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### 0x00158d0002e214d4 Power outage count
- **Entity ID**: `sensor.0x00158d0002e214d4_power_outage_count` | **State**: `7468`

## 0X00158D0002E2697E {#0x00158d0002e2697e}
*4 entities*

### 0x00158d0002e2697e Battery
- **Entity ID**: `sensor.0x00158d0002e2697e_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d0002e2697e Temperature
- **Entity ID**: `sensor.0x00158d0002e2697e_device_temperature` | **State**: `26`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Illuminance
- **Entity ID**: `sensor.0x00158d0002e2697e_illuminance_lux` | **State**: `unavailable`
- **Key Attributes**: device_class: illuminance, unit_of_measurement: lx, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0002e2697e Power outage count
- **Entity ID**: `sensor.0x00158d0002e2697e_power_outage_count` | **State**: `15571`

## 0X00158D0002Ea1003 {#0x00158d0002ea1003}
*2 entities*

### 0x00158d0002ea1003 Battery
- **Entity ID**: `sensor.0x00158d0002ea1003_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d0002ea1003 Power outage count
- **Entity ID**: `sensor.0x00158d0002ea1003_power_outage_count` | **State**: `172`

## 0X00158D00030423C7 {#0x00158d00030423c7}
*4 entities*

### Sensor pohybu pracovna battery
- **Entity ID**: `sensor.0x00158d00030423c7_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### Sensor pohybu pracovna device temperature
- **Entity ID**: `sensor.0x00158d00030423c7_device_temperature` | **State**: `26`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Sensor pohybu pracovna illuminance lux
- **Entity ID**: `sensor.0x00158d00030423c7_illuminance_lux` | **State**: `unavailable`
- **Key Attributes**: device_class: illuminance, unit_of_measurement: lx, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Sensor pohybu pracovna power outage count
- **Entity ID**: `sensor.0x00158d00030423c7_power_outage_count` | **State**: `2`

## 0X00158D0003140D60 {#0x00158d0003140d60}
*4 entities*

### 0x00158d0003140d60 Battery
- **Entity ID**: `sensor.0x00158d0003140d60_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d0003140d60 Temperature
- **Entity ID**: `sensor.0x00158d0003140d60_device_temperature` | **State**: `25`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Illuminance
- **Entity ID**: `sensor.0x00158d0003140d60_illuminance_lux` | **State**: `unavailable`
- **Key Attributes**: device_class: illuminance, unit_of_measurement: lx, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x00158d0003140d60 Power outage count
- **Entity ID**: `sensor.0x00158d0003140d60_power_outage_count` | **State**: `6612`

## 0X00158D000315C387 {#0x00158d000315c387}
*3 entities*

### 0x00158d000315c387 Battery
- **Entity ID**: `sensor.0x00158d000315c387_battery` | **State**: `97`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d000315c387 Temperature
- **Entity ID**: `sensor.0x00158d000315c387_device_temperature` | **State**: `30`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### 0x00158d000315c387 Power outage count
- **Entity ID**: `sensor.0x00158d000315c387_power_outage_count` | **State**: `75`

## 0X00158D000315Fac9 {#0x00158d000315fac9}
*3 entities*

### 0x00158d000315fac9 Battery
- **Entity ID**: `sensor.0x00158d000315fac9_battery` | **State**: `70`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### 0x00158d000315fac9 Temperature
- **Entity ID**: `sensor.0x00158d000315fac9_device_temperature` | **State**: `24`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### 0x00158d000315fac9 Power outage count
- **Entity ID**: `sensor.0x00158d000315fac9_power_outage_count` | **State**: `2597`

## 0X00158D000322B60F {#0x00158d000322b60f}
*4 entities*

### Teplotní čidlo půda battery
- **Entity ID**: `sensor.0x00158d000322b60f_battery` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### Teplotní čidlo půda humidity
- **Entity ID**: `sensor.0x00158d000322b60f_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

### Teplotní čidlo půda pressure
- **Entity ID**: `sensor.0x00158d000322b60f_pressure` | **State**: `unavailable`
- **Key Attributes**: device_class: atmospheric_pressure, unit_of_measurement: hPa, state_class: measurement

### Teplotní čidlo půda temperature
- **Entity ID**: `sensor.0x00158d000322b60f_temperature` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## 0X0017880104F1247D {#0x0017880104f1247d}
*2 entities*

### 0x0017880104f1247d Duration
- **Entity ID**: `sensor.0x0017880104f1247d_action_duration` | **State**: `unavailable`
- **Key Attributes**: device_class: duration, unit_of_measurement: s

### 0x0017880104f1247d Battery
- **Entity ID**: `sensor.0x0017880104f1247d_battery` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

## 0X0C4314Fffe5Fe450 {#0x0c4314fffe5fe450}
*3 entities*

### Tesla teplotni cidlo s displejem Battery
- **Entity ID**: `sensor.0x0c4314fffe5fe450_battery` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### Tesla teplotni cidlo s displejem Humidity
- **Entity ID**: `sensor.0x0c4314fffe5fe450_humidity` | **State**: `46.9`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

### Tesla teplotni cidlo s displejem Temperature
- **Entity ID**: `sensor.0x0c4314fffe5fe450_temperature` | **State**: `22.6`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## 0X84Fd27Fffea36224 {#0x84fd27fffea36224}
*9 entities*

### 0x84fd27fffea36224 Error status
- **Entity ID**: `sensor.0x84fd27fffea36224_error_status` | **State**: `0`

### Holiday start stop
- **Entity ID**: `sensor.0x84fd27fffea36224_holiday_start_stop` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### 0x84fd27fffea36224 Schedule friday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_friday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

### 0x84fd27fffea36224 Schedule monday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_monday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

### 0x84fd27fffea36224 Schedule saturday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_saturday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

### 0x84fd27fffea36224 Schedule sunday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_sunday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

### 0x84fd27fffea36224 Schedule thursday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_thursday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

### 0x84fd27fffea36224 Schedule tuesday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_tuesday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

### 0x84fd27fffea36224 Schedule wednesday
- **Entity ID**: `sensor.0x84fd27fffea36224_schedule_wednesday` | **State**: `06:00/17 12:00/21 14:00/17 17:00/21 24:00/17`

## 0Xa4C13887Ab4A50C0 {#0xa4c13887ab4a50c0}
*2 entities*

### 0xa4c13887ab4a50c0 Energy
- **Entity ID**: `sensor.0xa4c13887ab4a50c0_energy` | **State**: `56.82`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### 0xa4c13887ab4a50c0 Power
- **Entity ID**: `sensor.0xa4c13887ab4a50c0_power` | **State**: `0`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## Active {#active}
*5 entities*

### Active Power
- **Entity ID**: `sensor.active_power` | **State**: `3565`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Active Power L1
- **Entity ID**: `sensor.active_power_l1` | **State**: `559`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Active Power L2
- **Entity ID**: `sensor.active_power_l2` | **State**: `633`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Active Power L3
- **Entity ID**: `sensor.active_power_l3` | **State**: `2380`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Active Power Total
- **Entity ID**: `sensor.active_power_total` | **State**: `3573`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## Aktuální déšť {#aktualni}
- **Entity ID**: `sensor.aktualni_dest`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0
    - **unit_of_measurement**: mm

## Back {#back}
*13 entities*

### Back-up L1 Current
- **Entity ID**: `sensor.back_up_l1_current` | **State**: `0.2`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### Back-up L1 Frequency
- **Entity ID**: `sensor.back_up_l1_frequency` | **State**: `49.99`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### Back-up L1 Power
- **Entity ID**: `sensor.back_up_l1_power` | **State**: `0`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Back-up L1 Voltage
- **Entity ID**: `sensor.back_up_l1_voltage` | **State**: `237.5`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### Back-up L2 Current
- **Entity ID**: `sensor.back_up_l2_current` | **State**: `0.2`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### Back-up L2 Frequency
- **Entity ID**: `sensor.back_up_l2_frequency` | **State**: `50.01`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### Back-up L2 Power
- **Entity ID**: `sensor.back_up_l2_power` | **State**: `8`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Back-up L2 Voltage
- **Entity ID**: `sensor.back_up_l2_voltage` | **State**: `237.8`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### Back-up L3 Current
- **Entity ID**: `sensor.back_up_l3_current` | **State**: `0.3`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### Back-up L3 Frequency
- **Entity ID**: `sensor.back_up_l3_frequency` | **State**: `49.98`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### Back-up L3 Power
- **Entity ID**: `sensor.back_up_l3_power` | **State**: `12`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Back-up L3 Voltage
- **Entity ID**: `sensor.back_up_l3_voltage` | **State**: `241.1`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### Back-up Load
- **Entity ID**: `sensor.back_up_load` | **State**: `20`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## Backup {#backup}
*3 entities*

### Backup Backup Manager state
- **Entity ID**: `sensor.backup_backup_manager_state` | **State**: `idle`
- **Key Attributes**: device_class: enum
- **Other Attributes**:
    - **options**: [
        "idle",
        "create_backup",
        "blocked",
        "receive_backup",
        "restore_backup"
      ]

### Backup Last successful automatic backup
- **Entity ID**: `sensor.backup_last_successful_automatic_backup` | **State**: `2025-06-10T03:12:01+00:00`
- **Key Attributes**: device_class: timestamp

### Backup Next scheduled automatic backup
- **Entity ID**: `sensor.backup_next_scheduled_automatic_backup` | **State**: `2025-06-11T02:58:31+00:00`
- **Key Attributes**: device_class: timestamp

## Battery {#battery}
*32 entities*

### Battery BMS
- **Entity ID**: `sensor.battery_bms` | **State**: `255`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Charge Limit
- **Entity ID**: `sensor.battery_charge_limit` | **State**: `0`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### Battery Current
- **Entity ID**: `sensor.battery_current` | **State**: `-0.1`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### Battery Discharge Limit
- **Entity ID**: `sensor.battery_discharge_limit` | **State**: `18`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### Battery Error
- **Entity ID**: `sensor.battery_error` | **State**: ``

### Battery Error H
- **Entity ID**: `sensor.battery_error_h` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Error L
- **Entity ID**: `sensor.battery_error_l` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Hardware Version
- **Entity ID**: `sensor.battery_hardware_version` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Index
- **Entity ID**: `sensor.battery_index` | **State**: `257`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Max Cell Temperature
- **Entity ID**: `sensor.battery_max_cell_temperature` | **State**: `0.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Battery Max Cell Temperature ID
- **Entity ID**: `sensor.battery_max_cell_temperature_id` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Max Cell Voltage
- **Entity ID**: `sensor.battery_max_cell_voltage` | **State**: `0.0`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### Battery Max Cell Voltage ID
- **Entity ID**: `sensor.battery_max_cell_voltage_id` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Min Cell Temperature
- **Entity ID**: `sensor.battery_min_cell_temperature` | **State**: `0.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Battery Min Cell Temperature ID
- **Entity ID**: `sensor.battery_min_cell_temperature_id` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Min Cell Voltage
- **Entity ID**: `sensor.battery_min_cell_voltage` | **State**: `0.0`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### Battery Min Cell Voltage ID
- **Entity ID**: `sensor.battery_min_cell_voltage_id` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Mode
- **Entity ID**: `sensor.battery_mode` | **State**: `Discharge`

### Battery Mode code
- **Entity ID**: `sensor.battery_mode_code` | **State**: `2`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Modules
- **Entity ID**: `sensor.battery_modules` | **State**: `8`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Power
- **Entity ID**: `sensor.battery_power` | **State**: `-80`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Battery Protocol
- **Entity ID**: `sensor.battery_protocol` | **State**: `257`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Charge Hours
- **Entity ID**: `sensor.battery_remaining_charge_time` | **State**: `2.6625`
- **Key Attributes**: device_class: duration, unit_of_measurement: h

### Battery Software Version
- **Entity ID**: `sensor.battery_software_version` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery State of Charge
- **Entity ID**: `sensor.battery_state_of_charge` | **State**: `98`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### Battery State of Health
- **Entity ID**: `sensor.battery_state_of_health` | **State**: `99`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement

### Battery Status
- **Entity ID**: `sensor.battery_status` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Temperature
- **Entity ID**: `sensor.battery_temperature` | **State**: `35.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Battery Voltage
- **Entity ID**: `sensor.battery_voltage` | **State**: `410.7`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### Battery Warning
- **Entity ID**: `sensor.battery_warning` | **State**: ``

### Battery Warning H
- **Entity ID**: `sensor.battery_warning_h` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Battery Warning L
- **Entity ID**: `sensor.battery_warning_l` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## Bazénové čerpadlo dnes celkem {#bazenove}
- **Entity ID**: `sensor.bazenove_cerpadlo_dnes_celkem`
- **State**: `0.0`
- **Last Changed**: 2025-06-09 22:00:39
- **Last Updated**: 2025-06-09 22:00:39
- **Attributes**:
    - **device_class**: duration
    - **icon**: mdi:chart-line
    - **state_class**: measurement
    - **unit_of_measurement**: h

## Bridge state {#bridge}
- **Entity ID**: `sensor.bridge_state`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **icon**: mdi:router-wireless

## Bus Voltage {#bus}
- **Entity ID**: `sensor.bus_voltage`
- **State**: `780.7`
- **Last Changed**: 2025-06-10 07:13:53
- **Last Updated**: 2025-06-10 07:13:53
- **Attributes**:
    - **device_class**: voltage
    - **state_class**: measurement
    - **unit_of_measurement**: V

## hass.k8s.sklenarovi.cz Cert expiry {#cert}
- **Entity ID**: `sensor.cert_expiry_timestamp_hass_k8s_sklenarovi_cz`
- **State**: `2025-09-05T00:44:59+00:00`
- **Last Changed**: 2025-06-08 19:41:55
- **Last Updated**: 2025-06-08 19:41:55
- **Attributes**:
    - **device_class**: timestamp
    - **error**: None
    - **is_valid**: True

## Cesta {#cesta}
*4 entities*

### Cesta do Cloudfield z domova
- **Entity ID**: `sensor.cesta_do_cloudfield_z_domova` | **State**: `14`
- **Key Attributes**: device_class: duration, unit_of_measurement: min, state_class: measurement
- **Other Attributes**:
    - **attribution**: Powered by Waze
    - **destination**: 48.97293139690044,14.479641902726145
    - **distance**: 9.632
    - **duration**: 13.533333333333333
    - **origin**: 49.00313898,14.54330206
    - **route**: 34; Nádražní České Budějovice

### Cesta domů
- **Entity ID**: `sensor.cesta_domu` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min, state_class: measurement
- **Other Attributes**:
    - **attribution**: Powered by Waze

### Cesta na parkoviště
- **Entity ID**: `sensor.cesta_na_parkoviste` | **State**: `8`
- **Key Attributes**: device_class: duration, unit_of_measurement: min, state_class: measurement
- **Other Attributes**:
    - **attribution**: Powered by Waze
    - **destination**: 48.987333993970275,14.476771121844653
    - **distance**: 7.502
    - **duration**: 8.016666666666667
    - **origin**: 49.00313898,14.54330206
    - **route**: 34

### Cesta z Cloudfield domů
- **Entity ID**: `sensor.cesta_z_cloudfield_domu` | **State**: `12`
- **Key Attributes**: device_class: duration, unit_of_measurement: min, state_class: measurement
- **Other Attributes**:
    - **attribution**: Powered by Waze
    - **destination**: 49.00313898,14.54330206
    - **distance**: 9.105
    - **duration**: 12.333333333333334
    - **origin**: 48.97293139690044,14.479641902726145
    - **route**: 34

## Chladnicka {#chladnicka}
*4 entities*

### Chladnička
- **Entity ID**: `sensor.chladnicka` | **State**: `on`
- **Other Attributes**:
    - **door_open**: off
    - **eco_friendly**: off
    - **express_cool**: off
    - **express_freeze**: on
    - **freezer_temp**: -21
    - **fridge_temp**: 5
    - **supported_features**: 0
    - **temp_unit**: °C

### Chladnička Freezer temp
- **Entity ID**: `sensor.chladnicka_freezer_temp` | **State**: `-21`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **supported_features**: 0

### Chladnička Fridge temp
- **Entity ID**: `sensor.chladnicka_fridge_temp` | **State**: `5`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **supported_features**: 0

### Chladnička SSID
- **Entity ID**: `sensor.chladnicka_ssid` | **State**: `pap`
- **Other Attributes**:
    - **supported_features**: 0

## Commode {#commode}
- **Entity ID**: `sensor.commode`
- **State**: `3`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Current {#current}
*2 entities*

### Current Version
- **Entity ID**: `sensor.current_version` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Current Version
- **Entity ID**: `sensor.current_version_2` | **State**: `2025.5.3`

## Dest {#dest}
*2 entities*

### Déšť dnes celkem
- **Entity ID**: `sensor.dest_dnes_celkem` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: mm
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Déšť za 24 hodin
- **Entity ID**: `sensor.dest_za_24_hodin` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: mm
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Diag {#diag}
*2 entities*

### Diag Status
- **Entity ID**: `sensor.diag_status` | **State**: `BMS: Discharge current low, APP: Discharge current too low, Self-use load light, BMS: Charge disabled, PF value set`

### Diag Status Code
- **Entity ID**: `sensor.diag_status_code` | **State**: `33819008`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## System Monitor Disk usage / {#disk}
- **Entity ID**: `sensor.disk_use_percent`
- **State**: `67.4`
- **Last Changed**: 2025-06-10 05:45:45
- **Last Updated**: 2025-06-10 05:45:45
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: %

## Display {#display}
*3 entities*

### Display Weatherman - Display Last Update
- **Entity ID**: `sensor.display_weatherman_display_last_update` | **State**: `unavailable`
- **Key Attributes**: device_class: timestamp

### Display Weatherman - Recorded Display Refresh
- **Entity ID**: `sensor.display_weatherman_recorded_display_refresh` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: Refreshes, state_class: total_increasing

### Display Weatherman - WiFi Signal Strength
- **Entity ID**: `sensor.display_weatherman_wifi_signal_strength` | **State**: `unavailable`
- **Key Attributes**: device_class: signal_strength, unit_of_measurement: dBm, state_class: measurement

## Doba {#doba}
*2 entities*

### Doba praní pračky
- **Entity ID**: `sensor.doba_prani_pracky` | **State**: `Nepere`

### Doba sušení sušičky
- **Entity ID**: `sensor.doba_suseni_susicky` | **State**: `Nesuší`

## Energy {#energy}
*14 entities*

### Energy Buy
- **Entity ID**: `sensor.energy_buy` | **State**: `0`
- **Key Attributes**: device_class: power, unit_of_measurement: W

### energy_buy_daily
- **Entity ID**: `sensor.energy_buy_daily` | **State**: `0.2`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing
- **Other Attributes**:
    - **last_period**: 0.6
    - **last_reset**: 2025-06-09T22:00:00.002213+00:00
    - **last_valid_state**: 1018.8
    - **next_reset**: 2025-06-11T00:00:00+02:00
    - **status**: collecting

### sensor.energy_buy_daily_cost
- **Entity ID**: `sensor.energy_buy_daily_cost` | **State**: `unavailable`
- **Key Attributes**: device_class: monetary, unit_of_measurement: EUR, state_class: total
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### energy_buy_sum
- **Entity ID**: `sensor.energy_buy_sum` | **State**: `1018.8`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total
- **Other Attributes**:
    - **source**: sensor.energy_buy

### energy_buy_sum Cost
- **Entity ID**: `sensor.energy_buy_sum_cost` | **State**: `0.215999999999988`
- **Key Attributes**: device_class: monetary, unit_of_measurement: EUR, state_class: total
- **Other Attributes**:
    - **last_reset**: 2025-06-08T19:41:43.643071+00:00

### Solar production forecast Estimated energy production - this hour
- **Entity ID**: `sensor.energy_current_hour` | **State**: `0.002`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh

### Solar production forecast Estimated energy production - next hour
- **Entity ID**: `sensor.energy_next_hour` | **State**: `0.003`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh

### Solar production forecast Estimated energy production - today
- **Entity ID**: `sensor.energy_production_today` | **State**: `0.026`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh

### Solar production forecast Estimated energy production - remaining today
- **Entity ID**: `sensor.energy_production_today_remaining` | **State**: `0.023`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh

### Solar production forecast Estimated energy production - tomorrow
- **Entity ID**: `sensor.energy_production_tomorrow` | **State**: `0.029`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh

### Energy Sell
- **Entity ID**: `sensor.energy_sell` | **State**: `3572`
- **Key Attributes**: device_class: power, unit_of_measurement: W

### energy_sell_daily
- **Entity ID**: `sensor.energy_sell_daily` | **State**: `4.7`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing
- **Other Attributes**:
    - **last_period**: 42.5
    - **last_reset**: 2025-06-09T22:00:00.000609+00:00
    - **last_valid_state**: 6501.4
    - **next_reset**: 2025-06-11T00:00:00+02:00
    - **status**: collecting

### energy_sell_sum
- **Entity ID**: `sensor.energy_sell_sum` | **State**: `6501.4`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total
- **Other Attributes**:
    - **source**: sensor.energy_sell

### energy_sell_sum Compensation
- **Entity ID**: `sensor.energy_sell_sum_compensation` | **State**: `3.77600000000002`
- **Key Attributes**: device_class: monetary, unit_of_measurement: EUR, state_class: total
- **Other Attributes**:
    - **last_reset**: 2025-06-08T19:41:43.643533+00:00

## Error Codes {#error}
- **Entity ID**: `sensor.error_codes`
- **State**: `0`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Errors {#errors}
- **Entity ID**: `sensor.errors`
- **State**: ``
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46

## Function Bit {#function}
- **Entity ID**: `sensor.function_bit`
- **State**: `16416`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Grid {#grid}
*2 entities*

### Grid Mode
- **Entity ID**: `sensor.grid_mode` | **State**: `Connected to grid`

### Grid Mode code
- **Entity ID**: `sensor.grid_mode_code` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## Gw1100A {#gw1100a}
*12 entities*

### GW1100A Absolute Pressure
- **Entity ID**: `sensor.gw1100a_absolute_pressure` | **State**: `960.9`
- **Key Attributes**: device_class: pressure, unit_of_measurement: hPa, state_class: measurement

### GW1100A Dewpoint 1
- **Entity ID**: `sensor.gw1100a_dewpoint_1` | **State**: `10.1`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### GW1100A Dewpoint 2
- **Entity ID**: `sensor.gw1100a_dewpoint_2` | **State**: `11.7`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### GW1100A Humidity 1
- **Entity ID**: `sensor.gw1100a_humidity_1` | **State**: `68`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

### GW1100A Humidity 2
- **Entity ID**: `sensor.gw1100a_humidity_2` | **State**: `60`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

### GW1100A Indoor Dewpoint
- **Entity ID**: `sensor.gw1100a_indoor_dewpoint` | **State**: `12.2`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### GW1100A Indoor Humidity
- **Entity ID**: `sensor.gw1100a_indoor_humidity` | **State**: `40`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

### GW1100A Indoor Temperature
- **Entity ID**: `sensor.gw1100a_indoor_temperature` | **State**: `26.9`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### GW1100A Relative Pressure
- **Entity ID**: `sensor.gw1100a_relative_pressure` | **State**: `960.9`
- **Key Attributes**: device_class: pressure, unit_of_measurement: hPa, state_class: measurement

### GW1100A Temperature 1
- **Entity ID**: `sensor.gw1100a_temperature_1` | **State**: `16.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### GW1100A Temperature 2
- **Entity ID**: `sensor.gw1100a_temperature_2` | **State**: `19.7`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### GW1100A Temperature 3
- **Entity ID**: `sensor.gw1100a_temperature_3` | **State**: `18.9`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## House {#house}
*3 entities*

### House Consumption
- **Entity ID**: `sensor.house_consumption` | **State**: `312`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### house_consumption_daily
- **Entity ID**: `sensor.house_consumption_daily` | **State**: `2.2`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing
- **Other Attributes**:
    - **last_period**: 11.9
    - **last_reset**: 2025-06-09T22:00:00.003488+00:00
    - **last_valid_state**: 4519.0
    - **next_reset**: 2025-06-11T00:00:00+02:00
    - **status**: collecting

### house_consumption_sum
- **Entity ID**: `sensor.house_consumption_sum` | **State**: `4519.0`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total
- **Other Attributes**:
    - **source**: sensor.house_consumption

## Hp {#hp}
*5 entities*

### sensor.hp_color_laser_mfp_178nw
- **Entity ID**: `sensor.hp_color_laser_mfp_178nw` | **State**: `unavailable`
- **Key Attributes**: device_class: enum
- **Other Attributes**:
    - **options**: [
        "idle",
        "printing",
        "stopped"
      ]
    - **restored**: True
    - **supported_features**: 0

### Black Toner_S/N_:CRUM-230811A4D7A
- **Entity ID**: `sensor.hp_color_laser_mfp_178nw_black_toner_s_n_crum_220218a1a26` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Cyan Toner_S/N_:CRUM-240124A71FE
- **Entity ID**: `sensor.hp_color_laser_mfp_178nw_cyan_toner_s_n_crum_220218a12ff` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Magenta Toner_S/N_:CRUM-240109A9FE1
- **Entity ID**: `sensor.hp_color_laser_mfp_178nw_magenta_toner_s_n_crum_220218a0e0c` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Yellow Toner_S/N_:CRUM-200110A197A
- **Entity ID**: `sensor.hp_color_laser_mfp_178nw_yellow_toner_s_n_crum_220217aa8c3` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Xiaomi Gateway osvit {#illumination}
- **Entity ID**: `sensor.illumination_04cf8c983306`
- **State**: `957`
- **Last Changed**: 2025-06-10 06:38:53
- **Last Updated**: 2025-06-10 06:38:53
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: lm

## Inverter {#inverter}
*3 entities*

### Inverter Temperature (Air)
- **Entity ID**: `sensor.inverter_temperature_air` | **State**: `44.6`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Inverter Temperature (Module)
- **Entity ID**: `sensor.inverter_temperature_module` | **State**: `0.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

### Inverter Temperature (Radiator)
- **Entity ID**: `sensor.inverter_temperature_radiator` | **State**: `40.3`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## Iphone {#iphone}
*19 entities*

### iPhone SE (2nd generation) Activity
- **Entity ID**: `sensor.iphone_se_2nd_generation_activity` | **State**: `Stationary`
- **Other Attributes**:
    - **Confidence**: High
    - **Types**: [
        "Stationary"
      ]

### iPhone SE (2nd generation) App Version
- **Entity ID**: `sensor.iphone_se_2nd_generation_app_version` | **State**: `2025.5`

### iPhone SE (2nd generation) Audio Output
- **Entity ID**: `sensor.iphone_se_2nd_generation_audio_output` | **State**: `Built-in Speaker`

### iPhone SE (2nd generation) Average Active Pace
- **Entity ID**: `sensor.iphone_se_2nd_generation_average_active_pace` | **State**: `1`
- **Key Attributes**: unit_of_measurement: m/s

### iPhone SE (2nd generation) Battery Level
- **Entity ID**: `sensor.iphone_se_2nd_generation_battery_level` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %

### iPhone SE (2nd generation) Battery State
- **Entity ID**: `sensor.iphone_se_2nd_generation_battery_state` | **State**: `Full`
- **Other Attributes**:
    - **Low Power Mode**: False

### iPhone SE (2nd generation) BSSID
- **Entity ID**: `sensor.iphone_se_2nd_generation_bssid` | **State**: `Not Connected`

### iPhone SE (2nd generation) Connection Type
- **Entity ID**: `sensor.iphone_se_2nd_generation_connection_type` | **State**: `Wi-Fi`

### iPhone SE (2nd generation) Distance
- **Entity ID**: `sensor.iphone_se_2nd_generation_distance` | **State**: `0`
- **Key Attributes**: unit_of_measurement: m

### iPhone SE (2nd generation) Floors Ascended
- **Entity ID**: `sensor.iphone_se_2nd_generation_floors_ascended` | **State**: `0`
- **Key Attributes**: unit_of_measurement: floors

### iPhone SE (2nd generation) Floors Descended
- **Entity ID**: `sensor.iphone_se_2nd_generation_floors_descended` | **State**: `0`
- **Key Attributes**: unit_of_measurement: floors

### iPhone SE (2nd generation) Geocoded Location
- **Entity ID**: `sensor.iphone_se_2nd_generation_geocoded_location` | **State**: `Unknown`

### iPhone SE (2nd generation) Last Update Trigger
- **Entity ID**: `sensor.iphone_se_2nd_generation_last_update_trigger` | **State**: `Siri`

### iPhone SE (2nd generation) Location permission
- **Entity ID**: `sensor.iphone_se_2nd_generation_location_permission` | **State**: `Authorized when in use`

### iPhone SE (2nd generation) SIM 1
- **Entity ID**: `sensor.iphone_se_2nd_generation_sim_1` | **State**: `--`
- **Other Attributes**:
    - **Allows VoIP**: True
    - **Carrier ID**: 0000000100000001
    - **Carrier Name**: --
    - **Current Radio Technology**: Long-Term Evolution (LTE)
    - **ISO Country Code**: --
    - **Mobile Country Code**: 65535
    - **Mobile Network Code**: 65535

### iPhone SE (2nd generation) SIM 2
- **Entity ID**: `sensor.iphone_se_2nd_generation_sim_2` | **State**: `--`
- **Other Attributes**:
    - **Allows VoIP**: True
    - **Carrier ID**: 0000000100000002
    - **Carrier Name**: --
    - **ISO Country Code**: --
    - **Mobile Country Code**: 65535
    - **Mobile Network Code**: 65535

### iPhone SE (2nd generation) SSID
- **Entity ID**: `sensor.iphone_se_2nd_generation_ssid` | **State**: `Not Connected`

### iPhone SE (2nd generation) Steps
- **Entity ID**: `sensor.iphone_se_2nd_generation_steps` | **State**: `0`
- **Key Attributes**: unit_of_measurement: steps

### iPhone SE (2nd generation) Storage
- **Entity ID**: `sensor.iphone_se_2nd_generation_storage` | **State**: `1.71`
- **Key Attributes**: unit_of_measurement: % available
- **Other Attributes**:
    - **Available**: 2,38 GB
    - **Available (Important)**: 13,44 GB
    - **Available (Opportunistic)**: 1,10 GB
    - **Total**: 63,97 GB

## sensor.java_examples {#java}
- **Entity ID**: `sensor.java_examples`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Klimatizace Pokoj {#klimatizace-pokoj}
*27 entities*

### Klimatizace pokoj levý Energy current
- **Entity ID**: `sensor.klimatizace_pokoj_levy_energy_current` | **State**: `5`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement
- **Other Attributes**:
    - **supported_features**: 0

### Klimatizace pokoj levý Filter Remaining Life
- **Entity ID**: `sensor.klimatizace_pokoj_levy_filter_remaining_life` | **State**: `0`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **max_time**: 720
    - **supported_features**: 0
    - **use_time**: 720

### PM1
- **Entity ID**: `sensor.klimatizace_pokoj_levy_pm1` | **State**: `unavailable`
- **Key Attributes**: device_class: pm1, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### PM10
- **Entity ID**: `sensor.klimatizace_pokoj_levy_pm10` | **State**: `unavailable`
- **Key Attributes**: device_class: pm10, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### PM2.5
- **Entity ID**: `sensor.klimatizace_pokoj_levy_pm2_5` | **State**: `unavailable`
- **Key Attributes**: device_class: pm25, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Klimatizace pokoj levý Schedule turn-off
- **Entity ID**: `sensor.klimatizace_pokoj_levy_schedule_turn_off` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj levý Schedule turn-on
- **Entity ID**: `sensor.klimatizace_pokoj_levy_schedule_turn_on` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj levý Sleep time
- **Entity ID**: `sensor.klimatizace_pokoj_levy_sleep_time` | **State**: `0`
- **Key Attributes**: unit_of_measurement: min, state_class: duration
- **Other Attributes**:
    - **supported_features**: 0

### Klimatizace pokoj levý Sleep timer
- **Entity ID**: `sensor.klimatizace_pokoj_levy_sleep_timer` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj levý SSID
- **Entity ID**: `sensor.klimatizace_pokoj_levy_ssid` | **State**: `pap-int`
- **Other Attributes**:
    - **supported_features**: 0

### PM1
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_pm1` | **State**: `unavailable`
- **Key Attributes**: device_class: pm1, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### PM10
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_pm10` | **State**: `unavailable`
- **Key Attributes**: device_class: pm10, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### PM2.5
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_pm2_5` | **State**: `unavailable`
- **Key Attributes**: device_class: pm25, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Klimatizace pokoj pravý Schedule turn-off
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_schedule_turn_off` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj pravý Schedule turn-on
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_schedule_turn_on` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj pravý Sleep timer
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_sleep_timer` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj pravý SSID
- **Entity ID**: `sensor.klimatizace_pokoj_pravy_ssid` | **State**: `pap-int`
- **Other Attributes**:
    - **supported_features**: 0

### Klimatizace pokoj stred Energy current
- **Entity ID**: `sensor.klimatizace_pokoj_stred_energy_current` | **State**: `5`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement
- **Other Attributes**:
    - **supported_features**: 0

### Klimatizace pokoj stred Filter Remaining Life
- **Entity ID**: `sensor.klimatizace_pokoj_stred_filter_remaining_life` | **State**: `0`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **max_time**: 720
    - **supported_features**: 0
    - **use_time**: 720

### PM1
- **Entity ID**: `sensor.klimatizace_pokoj_stred_pm1` | **State**: `unavailable`
- **Key Attributes**: device_class: pm1, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### PM10
- **Entity ID**: `sensor.klimatizace_pokoj_stred_pm10` | **State**: `unavailable`
- **Key Attributes**: device_class: pm10, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### PM2.5
- **Entity ID**: `sensor.klimatizace_pokoj_stred_pm2_5` | **State**: `unavailable`
- **Key Attributes**: device_class: pm25, unit_of_measurement: µg/m³, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Klimatizace pokoj stred Schedule turn-off
- **Entity ID**: `sensor.klimatizace_pokoj_stred_schedule_turn_off` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj stred Schedule turn-on
- **Entity ID**: `sensor.klimatizace_pokoj_stred_schedule_turn_on` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj stred Sleep time
- **Entity ID**: `sensor.klimatizace_pokoj_stred_sleep_time` | **State**: `0`
- **Key Attributes**: unit_of_measurement: min, state_class: duration
- **Other Attributes**:
    - **supported_features**: 0

### Klimatizace pokoj stred Sleep timer
- **Entity ID**: `sensor.klimatizace_pokoj_stred_sleep_timer` | **State**: `unknown`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

### Klimatizace pokoj stred SSID
- **Entity ID**: `sensor.klimatizace_pokoj_stred_ssid` | **State**: `pap-int`
- **Other Attributes**:
    - **supported_features**: 0

## Klimatizace Pracovna {#klimatizace-pracovna}
*3 entities*

### Klimatizace pokoj pravý Energy current
- **Entity ID**: `sensor.klimatizace_pracovna_energy_current` | **State**: `5`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement
- **Other Attributes**:
    - **supported_features**: 0

### Klimatizace pokoj pravý Filter Remaining Life
- **Entity ID**: `sensor.klimatizace_pracovna_filter_remaining_life` | **State**: `0`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **max_time**: 720
    - **supported_features**: 0
    - **use_time**: 720

### Klimatizace pokoj pravý Sleep time
- **Entity ID**: `sensor.klimatizace_pracovna_sleep_time` | **State**: `0`
- **Key Attributes**: unit_of_measurement: min, state_class: duration
- **Other Attributes**:
    - **supported_features**: 0

## Kotel dnes celkem {#kotel}
- **Entity ID**: `sensor.kotel_dnes_celkem`
- **State**: `0.0`
- **Last Changed**: 2025-06-09 22:00:33
- **Last Updated**: 2025-06-09 22:00:33
- **Attributes**:
    - **device_class**: duration
    - **icon**: mdi:chart-line
    - **state_class**: measurement
    - **unit_of_measurement**: h

## System Monitor Last boot {#last}
- **Entity ID**: `sensor.last_boot`
- **State**: `2025-04-14T10:13:00+00:00`
- **Last Changed**: 2025-06-08 19:41:45
- **Last Updated**: 2025-06-08 19:41:45
- **Attributes**:
    - **device_class**: timestamp

## Load {#load}
*7 entities*

### Load
- **Entity ID**: `sensor.load` | **State**: `298`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Load L1
- **Entity ID**: `sensor.load_l1` | **State**: `101`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Load L2
- **Entity ID**: `sensor.load_l2` | **State**: `87`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Load L3
- **Entity ID**: `sensor.load_l3` | **State**: `129`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Load Mode L1
- **Entity ID**: `sensor.load_mode_l1` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Load Mode L2
- **Entity ID**: `sensor.load_mode_l2` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Load Mode L3
- **Entity ID**: `sensor.load_mode_l3` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## Lora {#lora}
*3 entities*

### Lora Bridge Up
- **Entity ID**: `sensor.lora_bridge_up` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement:  
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Lora Bridge Up
- **Entity ID**: `sensor.lora_bridge_up_2` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement:  
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Lora Bridge Up
- **Entity ID**: `sensor.lora_bridge_up_3` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement:  
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Lumi Lumi {#lumi-lumi}
*2 entities*

### sensor-uniku-vody-koupelna-nahore Battery
- **Entity ID**: `sensor.lumi_lumi_sensor_wleak_aq1_battery` | **State**: `77.0`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **battery_quantity**: 1
    - **battery_size**: CR2032
    - **battery_voltage**: 3.04

### sensor-uniku-vody-koupelna-nahore Device temperature
- **Entity ID**: `sensor.lumi_lumi_sensor_wleak_aq1_device_temperature` | **State**: `23.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## Manufacture Code {#manufacture}
- **Entity ID**: `sensor.manufacture_code`
- **State**: `10`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Memory {#memory}
*2 entities*

### System Monitor Memory free
- **Entity ID**: `sensor.memory_free` | **State**: `21224.6`
- **Key Attributes**: device_class: data_size, unit_of_measurement: MiB, state_class: measurement

### System Monitor Memory usage
- **Entity ID**: `sensor.memory_use_percent` | **State**: `33.3`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement

## Meter {#meter}
*16 entities*

### Meter Active Power L1
- **Entity ID**: `sensor.meter_active_power_l1` | **State**: `559`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Meter Active Power L2
- **Entity ID**: `sensor.meter_active_power_l2` | **State**: `633`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Meter Active Power L3
- **Entity ID**: `sensor.meter_active_power_l3` | **State**: `2380`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Meter Active Power Total
- **Entity ID**: `sensor.meter_active_power_total` | **State**: `3573`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Meter Communication Status
- **Entity ID**: `sensor.meter_communication_status` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Frequency
- **Entity ID**: `sensor.meter_frequency` | **State**: `49.99`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### Meter Power Factor
- **Entity ID**: `sensor.meter_power_factor` | **State**: `0.917`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Power Factor L1
- **Entity ID**: `sensor.meter_power_factor_l1` | **State**: `0.723`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Power Factor L2
- **Entity ID**: `sensor.meter_power_factor_l2` | **State**: `0.863`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Power Factor L3
- **Entity ID**: `sensor.meter_power_factor_l3` | **State**: `0.997`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Software Version
- **Entity ID**: `sensor.meter_software_version` | **State**: `80`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Test Status
- **Entity ID**: `sensor.meter_test_status` | **State**: `0`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### Meter Total Energy (export)
- **Entity ID**: `sensor.meter_total_energy_export` | **State**: `6360.23`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Meter Total Energy (import)
- **Entity ID**: `sensor.meter_total_energy_import` | **State**: `1585.56`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### sensor.meter_total_energy_import_cost
- **Entity ID**: `sensor.meter_total_energy_import_cost` | **State**: `unavailable`
- **Key Attributes**: device_class: monetary, unit_of_measurement: EUR, state_class: total
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Meter Type
- **Entity ID**: `sensor.meter_type` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## My {#my}
*2 entities*

### My Humidity
- **Entity ID**: `sensor.my_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### My Temperature
- **Entity ID**: `sensor.my_temperature` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Current {#my-tp}
- **Entity ID**: `sensor.my_tp_switch_amps`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **unit_of_measurement**: A

## NBus Voltage {#nbus}
- **Entity ID**: `sensor.nbus_voltage`
- **State**: `390.4`
- **Last Changed**: 2025-06-10 07:13:53
- **Last Updated**: 2025-06-10 07:13:53
- **Attributes**:
    - **device_class**: voltage
    - **state_class**: measurement
    - **unit_of_measurement**: V

## Netatmo Moje {#netatmo-moje}
*16 entities*

### Weather Station Carbon dioxide
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_co2` | **State**: `674`
- **Key Attributes**: device_class: carbon_dioxide, unit_of_measurement: ppm, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Humidity
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_humidity` | **State**: `49`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Noise
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_noise` | **State**: `34`
- **Key Attributes**: device_class: sound_pressure, unit_of_measurement: dB, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Battery
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_battery_percent` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Humidity
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### RF strength
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_radio` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Moje domácnost (Weather Station) Outdoor Module Radio Level
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_radio_level` | **State**: `unavailable`
- **Key Attributes**: device_class: signal_strength, unit_of_measurement: dBm, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Reachability
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_reachability` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Temperature
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_temperature` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Temperature trend
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_outdoor_module_temperature_trend` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Weather Station Atmospheric pressure
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_pressure` | **State**: `1020.5`
- **Key Attributes**: device_class: atmospheric_pressure, unit_of_measurement: mbar, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Pressure trend
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_pressure_trend` | **State**: `down`
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Reachability
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_reachability` | **State**: `True`
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Temperature
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_temperature` | **State**: `23.4`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Temperature trend
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_temperature_trend` | **State**: `stable`
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Wi-Fi strength
- **Entity ID**: `sensor.netatmo_moje_domacnost_weather_station_wifi` | **State**: `High`
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

## On {#on}
*14 entities*

### On-grid L1 Current
- **Entity ID**: `sensor.on_grid_l1_current` | **State**: `3.0`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### On-grid L1 Frequency
- **Entity ID**: `sensor.on_grid_l1_frequency` | **State**: `49.99`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### On-grid L1 Power
- **Entity ID**: `sensor.on_grid_l1_power` | **State**: `658`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### On-grid L1 Voltage
- **Entity ID**: `sensor.on_grid_l1_voltage` | **State**: `237.6`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### On-grid L2 Current
- **Entity ID**: `sensor.on_grid_l2_current` | **State**: `3.1`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### On-grid L2 Frequency
- **Entity ID**: `sensor.on_grid_l2_frequency` | **State**: `50.01`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### On-grid L2 Power
- **Entity ID**: `sensor.on_grid_l2_power` | **State**: `725`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### On-grid L2 Voltage
- **Entity ID**: `sensor.on_grid_l2_voltage` | **State**: `238.0`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### On-grid L3 Current
- **Entity ID**: `sensor.on_grid_l3_current` | **State**: `10.4`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### On-grid L3 Frequency
- **Entity ID**: `sensor.on_grid_l3_frequency` | **State**: `49.98`
- **Key Attributes**: device_class: frequency, unit_of_measurement: Hz, state_class: measurement

### On-grid L3 Power
- **Entity ID**: `sensor.on_grid_l3_power` | **State**: `2517`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### On-grid L3 Voltage
- **Entity ID**: `sensor.on_grid_l3_voltage` | **State**: `241.0`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

### On-grid Mode
- **Entity ID**: `sensor.on_grid_mode` | **State**: `Exporting`

### On-grid Mode code
- **Entity ID**: `sensor.on_grid_mode_code` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## Openweathermap {#openweathermap}
*27 entities*

### OpenWeatherMap Cloud coverage
- **Entity ID**: `sensor.openweathermap_cloud_coverage` | **State**: `15`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Condition
- **Entity ID**: `sensor.openweathermap_condition` | **State**: `partlycloudy`
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Dew Point
- **Entity ID**: `sensor.openweathermap_dew_point` | **State**: `11.81`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Feels like temperature
- **Entity ID**: `sensor.openweathermap_feels_like_temperature` | **State**: `14.9`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Forecast Cloud coverage
- **Entity ID**: `sensor.openweathermap_forecast_cloud_coverage` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Cloud coverage
- **Entity ID**: `sensor.openweathermap_forecast_cloud_coverage_2` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Condition
- **Entity ID**: `sensor.openweathermap_forecast_condition` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Precipitation
- **Entity ID**: `sensor.openweathermap_forecast_precipitation` | **State**: `unavailable`
- **Key Attributes**: device_class: precipitation, unit_of_measurement: mm
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Precipitation probability
- **Entity ID**: `sensor.openweathermap_forecast_precipitation_probability` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Pressure
- **Entity ID**: `sensor.openweathermap_forecast_pressure` | **State**: `unavailable`
- **Key Attributes**: device_class: pressure, unit_of_measurement: hPa
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Temperature
- **Entity ID**: `sensor.openweathermap_forecast_temperature` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Temperature Low
- **Entity ID**: `sensor.openweathermap_forecast_temperature_low` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Time
- **Entity ID**: `sensor.openweathermap_forecast_time` | **State**: `unavailable`
- **Key Attributes**: device_class: timestamp
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Wind bearing
- **Entity ID**: `sensor.openweathermap_forecast_wind_bearing` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Forecast Wind speed
- **Entity ID**: `sensor.openweathermap_forecast_wind_speed` | **State**: `unavailable`
- **Key Attributes**: device_class: wind_speed, unit_of_measurement: m/s
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### OpenWeatherMap Humidity
- **Entity ID**: `sensor.openweathermap_humidity` | **State**: `80`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Precipitation kind
- **Entity ID**: `sensor.openweathermap_precipitation_kind` | **State**: `None`
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Pressure
- **Entity ID**: `sensor.openweathermap_pressure` | **State**: `1021`
- **Key Attributes**: device_class: pressure, unit_of_measurement: hPa, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Rain
- **Entity ID**: `sensor.openweathermap_rain` | **State**: `0`
- **Key Attributes**: device_class: precipitation_intensity, unit_of_measurement: mm/h, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Snow
- **Entity ID**: `sensor.openweathermap_snow` | **State**: `0`
- **Key Attributes**: device_class: precipitation_intensity, unit_of_measurement: mm/h, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Temperature
- **Entity ID**: `sensor.openweathermap_temperature` | **State**: `15.23`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap UV Index
- **Entity ID**: `sensor.openweathermap_uv_index` | **State**: `2.3`
- **Key Attributes**: unit_of_measurement: UV index, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Visibility
- **Entity ID**: `sensor.openweathermap_visibility` | **State**: `10000`
- **Key Attributes**: device_class: distance, unit_of_measurement: m, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Weather
- **Entity ID**: `sensor.openweathermap_weather` | **State**: `few clouds`
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Weather Code
- **Entity ID**: `sensor.openweathermap_weather_code` | **State**: `801`
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Wind bearing
- **Entity ID**: `sensor.openweathermap_wind_bearing` | **State**: `0`
- **Key Attributes**: device_class: wind_direction, unit_of_measurement: °, state_class: measurement_angle
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

### OpenWeatherMap Wind speed
- **Entity ID**: `sensor.openweathermap_wind_speed` | **State**: `0.89`
- **Key Attributes**: device_class: wind_speed, unit_of_measurement: m/s, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by OpenWeatherMap

## Operation Mode code {#operation}
- **Entity ID**: `sensor.operation_mode_code`
- **State**: `0`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Pavel {#pavel}
*60 entities*

### Pavel - iPad (2) Activity
- **Entity ID**: `sensor.pavel_ipad_2_activity` | **State**: `Stationary`
- **Other Attributes**:
    - **Confidence**: High
    - **Types**: [
        "Stationary"
      ]

### Pavel - iPad (2) App Version
- **Entity ID**: `sensor.pavel_ipad_2_app_version` | **State**: `2025.5`

### Pavel - iPad (2) Audio Output
- **Entity ID**: `sensor.pavel_ipad_2_audio_output` | **State**: `Built-in Speaker`

### Pavel - iPad (2) Battery Level
- **Entity ID**: `sensor.pavel_ipad_2_battery_level` | **State**: `70`
- **Key Attributes**: device_class: battery, unit_of_measurement: %

### Pavel - iPad (2) Battery State
- **Entity ID**: `sensor.pavel_ipad_2_battery_state` | **State**: `Not Charging`
- **Other Attributes**:
    - **Low Power Mode**: False

### Pavel - iPad (2) BSSID
- **Entity ID**: `sensor.pavel_ipad_2_bssid` | **State**: `Not Connected`

### Pavel - iPad (2) Connection Type
- **Entity ID**: `sensor.pavel_ipad_2_connection_type` | **State**: `Wi-Fi`

### Pavel - iPad (2) Geocoded Location
- **Entity ID**: `sensor.pavel_ipad_2_geocoded_location` | **State**: `Horní 267
373 71 Adamov
Česko`
- **Other Attributes**:
    - **Administrative Area**: Jihočeský kraj
    - **Areas Of Interest**: N/A
    - **Country**: Česko
    - **ISO Country Code**: CZ
    - **Inland Water**: N/A
    - **Locality**: Adamov
    - **Location**: [
        49.00318011031788,
        14.54324980455728
      ]
    - **Name**: Horní 267
    - **Ocean**: N/A
    - **Postal Code**: 373 71
    - **Sub Administrative Area**: Okres České Budějovice
    - **Sub Locality**: Adamov
    - **Sub Thoroughfare**: 267
    - **Thoroughfare**: Horní
    - **Time Zone**: Europe/Prague
    - **Zones**: [
        "Home"
      ]

### Pavel - iPad (2) Last Update Trigger
- **Entity ID**: `sensor.pavel_ipad_2_last_update_trigger` | **State**: `Siri`

### Pavel - iPad (2) Location permission
- **Entity ID**: `sensor.pavel_ipad_2_location_permission` | **State**: `Authorized when in use`

### Pavel - iPad (2) SSID
- **Entity ID**: `sensor.pavel_ipad_2_ssid` | **State**: `Not Connected`

### Pavel - iPad (2) Storage
- **Entity ID**: `sensor.pavel_ipad_2_storage` | **State**: `43.07`
- **Key Attributes**: unit_of_measurement: % available
- **Other Attributes**:
    - **Available**: 30,62 GB
    - **Available (Important)**: 72,02 GB
    - **Available (Opportunistic)**: 54,95 GB
    - **Total**: 127,59 GB

### Pavel - iPad Activity
- **Entity ID**: `sensor.pavel_ipad_activity` | **State**: `Stationary`
- **Other Attributes**:
    - **Confidence**: High
    - **Types**: [
        "Stationary"
      ]

### Pavel - iPad App Version
- **Entity ID**: `sensor.pavel_ipad_app_version` | **State**: `2025.5`

### Pavel - iPad Audio Output
- **Entity ID**: `sensor.pavel_ipad_audio_output` | **State**: `Built-in Speaker`

### Pavel - iPad Battery Level
- **Entity ID**: `sensor.pavel_ipad_battery_level` | **State**: `5`
- **Key Attributes**: device_class: battery, unit_of_measurement: %

### Pavel - iPad Battery State
- **Entity ID**: `sensor.pavel_ipad_battery_state` | **State**: `Not Charging`
- **Other Attributes**:
    - **Low Power Mode**: False

### Pavel - iPad BSSID
- **Entity ID**: `sensor.pavel_ipad_bssid` | **State**: `Not Connected`

### Pavel - iPad Connection Type
- **Entity ID**: `sensor.pavel_ipad_connection_type` | **State**: `Wi-Fi`

### Pavel - iPad Geocoded Location
- **Entity ID**: `sensor.pavel_ipad_geocoded_location` | **State**: `Horní 267
373 71 Adamov
Česko`
- **Other Attributes**:
    - **Administrative Area**: Jihočeský kraj
    - **Areas Of Interest**: N/A
    - **Country**: Česko
    - **ISO Country Code**: CZ
    - **Inland Water**: N/A
    - **Locality**: Adamov
    - **Location**: [
        49.003143310546875,
        14.543368393424155
      ]
    - **Name**: Horní 267
    - **Ocean**: N/A
    - **Postal Code**: 373 71
    - **Sub Administrative Area**: Okres České Budějovice
    - **Sub Locality**: Adamov
    - **Sub Thoroughfare**: 267
    - **Thoroughfare**: Horní
    - **Time Zone**: Europe/Prague
    - **Zones**: [
        "Home"
      ]

### Pavel - iPad Last Update Trigger
- **Entity ID**: `sensor.pavel_ipad_last_update_trigger` | **State**: `Launch`

### Pavel - iPad Location permission
- **Entity ID**: `sensor.pavel_ipad_location_permission` | **State**: `Authorized when in use`

### Pavel - iPad SSID
- **Entity ID**: `sensor.pavel_ipad_ssid` | **State**: `Not Connected`

### Pavel - iPad Storage
- **Entity ID**: `sensor.pavel_ipad_storage` | **State**: `12.39`
- **Key Attributes**: unit_of_measurement: % available
- **Other Attributes**:
    - **Available**: 4,28 GB
    - **Available (Important)**: 5,61 GB
    - **Available (Opportunistic)**: 3,96 GB
    - **Total**: 31,99 GB

### Pavel - iPhone Activity
- **Entity ID**: `sensor.pavel_iphone_activity` | **State**: `Stationary`
- **Other Attributes**:
    - **Confidence**: High
    - **Types**: [
        "Stationary"
      ]

### Pavel - iPhone App Version
- **Entity ID**: `sensor.pavel_iphone_app_version` | **State**: `2025.5`

### Pavel - iPhone Audio Output
- **Entity ID**: `sensor.pavel_iphone_audio_output` | **State**: `Built-in Speaker`

### Pavel - iPhone Average Active Pace
- **Entity ID**: `sensor.pavel_iphone_average_active_pace` | **State**: `1`
- **Key Attributes**: unit_of_measurement: m/s

### Pavel - iPhone Battery Level
- **Entity ID**: `sensor.pavel_iphone_battery_level` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %

### Pavel - iPhone Battery State
- **Entity ID**: `sensor.pavel_iphone_battery_state` | **State**: `Not Charging`
- **Other Attributes**:
    - **Low Power Mode**: False

### Pavel - iPhone BSSID
- **Entity ID**: `sensor.pavel_iphone_bssid` | **State**: `18:e8:29:a2:3e:76`

### Pavel - iPhone Connection Type
- **Entity ID**: `sensor.pavel_iphone_connection_type` | **State**: `Wi-Fi`

### Pavel - iPhone Distance
- **Entity ID**: `sensor.pavel_iphone_distance` | **State**: `13`
- **Key Attributes**: unit_of_measurement: m

### Pavel - iPhone Floors Ascended
- **Entity ID**: `sensor.pavel_iphone_floors_ascended` | **State**: `0`
- **Key Attributes**: unit_of_measurement: floors

### Pavel - iPhone Floors Descended
- **Entity ID**: `sensor.pavel_iphone_floors_descended` | **State**: `1`
- **Key Attributes**: unit_of_measurement: floors

### Pavel - iPhone Geocoded Location
- **Entity ID**: `sensor.pavel_iphone_geocoded_location` | **State**: `Horní 267
373 71 Adamov
Česko`
- **Other Attributes**:
    - **Administrative Area**: Jihočeský kraj
    - **Areas Of Interest**: N/A
    - **Country**: Česko
    - **ISO Country Code**: CZ
    - **Inland Water**: N/A
    - **Locality**: Adamov
    - **Location**: [
        49.00318076813675,
        14.543385514615379
      ]
    - **Name**: Horní 267
    - **Ocean**: N/A
    - **Postal Code**: 373 71
    - **Sub Administrative Area**: Okres České Budějovice
    - **Sub Locality**: Adamov
    - **Sub Thoroughfare**: 267
    - **Thoroughfare**: Horní
    - **Time Zone**: Europe/Prague
    - **Zones**: [
        "Home"
      ]

### Pavel - iPhone Last Update Trigger
- **Entity ID**: `sensor.pavel_iphone_last_update_trigger` | **State**: `Background Fetch`

### Pavel - iPhone Location permission
- **Entity ID**: `sensor.pavel_iphone_location_permission` | **State**: `Authorized Always`

### Pavel - iPhone SIM 1
- **Entity ID**: `sensor.pavel_iphone_sim_1` | **State**: `--`
- **Other Attributes**:
    - **Allows VoIP**: True
    - **Carrier ID**: 0000000100000001
    - **Carrier Name**: --
    - **Current Radio Technology**: 5G Non-Standalone
    - **ISO Country Code**: --
    - **Mobile Country Code**: 65535
    - **Mobile Network Code**: 65535

### Pavel - iPhone SIM 2
- **Entity ID**: `sensor.pavel_iphone_sim_2` | **State**: `--`
- **Other Attributes**:
    - **Allows VoIP**: True
    - **Carrier ID**: 0000000100000002
    - **Carrier Name**: --
    - **Current Radio Technology**: Long-Term Evolution (LTE)
    - **ISO Country Code**: --
    - **Mobile Country Code**: 65535
    - **Mobile Network Code**: 65535

### Pavel - iPhone SSID
- **Entity ID**: `sensor.pavel_iphone_ssid` | **State**: `pap-int`

### Pavel - iPhone Steps
- **Entity ID**: `sensor.pavel_iphone_steps` | **State**: `16`
- **Key Attributes**: unit_of_measurement: steps

### Pavel - iPhone Storage
- **Entity ID**: `sensor.pavel_iphone_storage` | **State**: `6.63`
- **Key Attributes**: unit_of_measurement: % available
- **Other Attributes**:
    - **Available**: 9,50 GB
    - **Available (Important)**: 25,23 GB
    - **Available (Opportunistic)**: 8,48 GB
    - **Total**: 127,87 GB

### Pavel - iPhone Watch Battery Level
- **Entity ID**: `sensor.pavel_iphone_watch_battery_level` | **State**: `85`
- **Key Attributes**: device_class: battery, unit_of_measurement: %

### Pavel - iPhone Watch Battery State
- **Entity ID**: `sensor.pavel_iphone_watch_battery_state` | **State**: `Not Charging`

### Pavel - MacBook Pro Active Audio Input
- **Entity ID**: `sensor.pavel_macbook_pro_active_audio_input` | **State**: `Inactive`
- **Other Attributes**:
    - **Active Audio Input**: []
    - **All Audio Input**: [
        "MacBook Pro Microphone"
      ]

### Pavel - MacBook Pro Active Audio Output
- **Entity ID**: `sensor.pavel_macbook_pro_active_audio_output` | **State**: `Inactive`
- **Other Attributes**:
    - **Active Audio Output**: []
    - **All Audio Output**: [
        "MacBook Pro Speakers"
      ]

### Pavel - MacBook Pro Active Camera
- **Entity ID**: `sensor.pavel_macbook_pro_active_camera` | **State**: `Inactive`
- **Other Attributes**:
    - **Active Camera**: []
    - **All Camera**: [
        "FaceTime HD Camera (Built-in)"
      ]

### Pavel - MacBook Pro BSSID
- **Entity ID**: `sensor.pavel_macbook_pro_bssid` | **State**: `74:83:c2:2b:62:46`

### Pavel - MacBook Pro Connection Type
- **Entity ID**: `sensor.pavel_macbook_pro_connection_type` | **State**: `Wi-Fi`
- **Other Attributes**:
    - **Hardware Address**: f8:ff:c2:5f:ab:9a
    - **Name**: Wi-Fi

### Pavel - MacBook Pro Displays
- **Entity ID**: `sensor.pavel_macbook_pro_displays` | **State**: `1`
- **Other Attributes**:
    - **Display IDs**: [
        "B716135C-AB3C-74F0-4F0B-597ADEBC5947"
      ]
    - **Display Names**: [
        "Built-in Retina Display"
      ]

### Pavel - MacBook Pro Frontmost App
- **Entity ID**: `sensor.pavel_macbook_pro_frontmost_app` | **State**: `loginwindow`
- **Other Attributes**:
    - **Bundle Identifier**: com.apple.loginwindow
    - **Is Hidden**: False
    - **Launch Date**: N/A
    - **Owns Menu Bar**: False

### Pavel - MacBook Pro Geocoded Location
- **Entity ID**: `sensor.pavel_macbook_pro_geocoded_location` | **State**: `Horní 266
373 71 Adamov
Czechia`
- **Other Attributes**:
    - **Administrative Area**: 31
    - **Areas Of Interest**: N/A
    - **Country**: Czechia
    - **ISO Country Code**: CZ
    - **Inland Water**: N/A
    - **Locality**: Adamov
    - **Location**: [
        49.00312123114568,
        14.543450878571015
      ]
    - **Name**: Horní 266
    - **Ocean**: N/A
    - **Postal Code**: 373 71
    - **Sub Administrative Area**: České Budějovice
    - **Sub Locality**: Adamov
    - **Sub Thoroughfare**: 266
    - **Thoroughfare**: Horní
    - **Time Zone**: Europe/Prague
    - **Zones**: [
        "Home"
      ]

### Pavel - MacBook Pro Internal Battery Level
- **Entity ID**: `sensor.pavel_macbook_pro_internal_battery_level` | **State**: `31`
- **Key Attributes**: device_class: battery, unit_of_measurement: %
- **Other Attributes**:
    - **Battery Provides Time Remaining**: True
    - **BatteryHealth**: Good
    - **BatteryHealthCondition**: 
    - **Current**: -396
    - **Current Capacity**: 31
    - **DesignCycleCount**: 1000
    - **Hardware Serial Number**: D869437A259K7LN5Z
    - **Is Charged**: False
    - **Is Charging**: False
    - **Is Present**: True
    - **LPM Active**: False
    - **Max Capacity**: 100
    - **Name**: InternalBattery-0
    - **Optimized Battery Charging Engaged**: False
    - **Power Source ID**: 7340131
    - **Power Source State**: AC Power
    - **Time to Empty**: 0
    - **Time to Full Charge**: 0
    - **Transport Type**: Internal
    - **Type**: InternalBattery

### Pavel - MacBook Pro Internal Battery State
- **Entity ID**: `sensor.pavel_macbook_pro_internal_battery_state` | **State**: `Not Charging`
- **Other Attributes**:
    - **Battery Provides Time Remaining**: True
    - **BatteryHealth**: Good
    - **BatteryHealthCondition**: 
    - **Current**: -396
    - **Current Capacity**: 31
    - **DesignCycleCount**: 1000
    - **Hardware Serial Number**: D869437A259K7LN5Z
    - **Is Charged**: False
    - **Is Charging**: False
    - **Is Present**: True
    - **LPM Active**: False
    - **Low Power Mode**: False
    - **Max Capacity**: 100
    - **Name**: InternalBattery-0
    - **Optimized Battery Charging Engaged**: False
    - **Power Source ID**: 7340131
    - **Power Source State**: AC Power
    - **Time to Empty**: 0
    - **Time to Full Charge**: 0
    - **Transport Type**: Internal
    - **Type**: InternalBattery

### Pavel - MacBook Pro Last Update Trigger
- **Entity ID**: `sensor.pavel_macbook_pro_last_update_trigger` | **State**: `Signaled`

### Pavel - MacBook Pro Primary Display ID
- **Entity ID**: `sensor.pavel_macbook_pro_primary_display_id` | **State**: `B716135C-AB3C-74F0-4F0B-597ADEBC5947`

### Pavel - MacBook Pro Primary Display Name
- **Entity ID**: `sensor.pavel_macbook_pro_primary_display_name` | **State**: `Built-in Retina Display`

### Pavel - MacBook Pro SSID
- **Entity ID**: `sensor.pavel_macbook_pro_ssid` | **State**: `pap-int`

### Pavel - MacBook Pro Storage
- **Entity ID**: `sensor.pavel_macbook_pro_storage` | **State**: `7.61`
- **Key Attributes**: unit_of_measurement: % available
- **Other Attributes**:
    - **Available**: 44,63 GB
    - **Available (Important)**: 145,66 GB
    - **Available (Opportunistic)**: 38,05 GB
    - **Total**: 499,96 GB

## Pavels {#pavels}
*18 entities*

### Pavel’s MacBook Pro Active Audio Input
- **Entity ID**: `sensor.pavels_macbook_pro_active_audio_input` | **State**: `Inactive`
- **Other Attributes**:
    - **Active Audio Input**: []
    - **All Audio Input**: [
        "Pavel - iPhone Microphone",
        "Logitech Webcam C925e",
        "MacBook Pro Microphone",
        "Microsoft Teams Audio"
      ]

### Pavel’s MacBook Pro Active Audio Output
- **Entity ID**: `sensor.pavels_macbook_pro_active_audio_output` | **State**: `MacBook Pro Speakers`
- **Other Attributes**:
    - **Active Audio Output**: [
        "MacBook Pro Speakers"
      ]
    - **All Audio Output**: [
        "LS27A600U",
        "MacBook Pro Speakers",
        "CADefaultDeviceAggregate-43922-0",
        "Microsoft Teams Audio"
      ]

### Pavel’s MacBook Pro Active Camera
- **Entity ID**: `sensor.pavels_macbook_pro_active_camera` | **State**: `Inactive`
- **Other Attributes**:
    - **Active Camera**: []
    - **All Camera**: [
        "Logitech Webcam C925e",
        "FaceTime HD Camera",
        "Pavel - iPhone Camera",
        "Pavel - iPhone Desk View Camera"
      ]

### Pavel’s MacBook Pro App Version
- **Entity ID**: `sensor.pavels_macbook_pro_app_version` | **State**: `2025.5`

### Pavel’s MacBook Pro Audio Output
- **Entity ID**: `sensor.pavels_macbook_pro_audio_output` | **State**: `Built-in Speaker`

### Pavel’s MacBook Pro BSSID
- **Entity ID**: `sensor.pavels_macbook_pro_bssid` | **State**: `18:e8:29:a2:3e:76`

### Pavel’s MacBook Pro Connection Type
- **Entity ID**: `sensor.pavels_macbook_pro_connection_type` | **State**: `Ethernet`
- **Other Attributes**:
    - **Hardware Address**: b8:b4:09:88:23:fe
    - **Name**: USB 10/100/1000 LAN

### Pavel’s MacBook Pro Displays
- **Entity ID**: `sensor.pavels_macbook_pro_displays` | **State**: `3`
- **Other Attributes**:
    - **Display IDs**: [
        "F6416D00-479E-47B8-9E25-CC1F359F7AE3",
        "37D8832A-2D66-02CA-B9F7-8F30A301B230",
        "2933F293-9126-4E9D-86BE-6F8851172479"
      ]
    - **Display Names**: [
        "LS32R75",
        "Built-in Retina Display",
        "LS27A600U"
      ]

### Pavel’s MacBook Pro Frontmost App
- **Entity ID**: `sensor.pavels_macbook_pro_frontmost_app` | **State**: `iTerm2`
- **Other Attributes**:
    - **Bundle Identifier**: com.googlecode.iterm2
    - **Is Hidden**: False
    - **Launch Date**: 2025-06-04T10:34:20Z
    - **Owns Menu Bar**: True

### Pavel’s MacBook Pro Geocoded Location
- **Entity ID**: `sensor.pavels_macbook_pro_geocoded_location` | **State**: `Horní 267
373 71 Adamov
Czechia`
- **Other Attributes**:
    - **Administrative Area**: South Bohemian Region
    - **Areas Of Interest**: N/A
    - **Country**: Czechia
    - **ISO Country Code**: CZ
    - **Inland Water**: N/A
    - **Locality**: Adamov
    - **Location**: [
        49.00314307970769,
        14.543417689031868
      ]
    - **Name**: Horní 267
    - **Ocean**: N/A
    - **Postal Code**: 373 71
    - **Sub Administrative Area**: České Budějovice
    - **Sub Locality**: Adamov
    - **Sub Thoroughfare**: 267
    - **Thoroughfare**: Horní
    - **Time Zone**: Europe/Prague
    - **Zones**: [
        "Home"
      ]

### Pavel’s MacBook Pro Internal Battery Level
- **Entity ID**: `sensor.pavels_macbook_pro_internal_battery_level` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %
- **Other Attributes**:
    - **Battery Provides Time Remaining**: True
    - **BatteryHealth**: Good
    - **BatteryHealthCondition**: 
    - **Current**: 0
    - **Current Capacity**: 100
    - **DesignCycleCount**: 1000
    - **Hardware Serial Number**: F8YH2V00RPK00000E4
    - **Is Charged**: True
    - **Is Charging**: False
    - **Is Present**: True
    - **LPM Active**: False
    - **Max Capacity**: 100
    - **Name**: InternalBattery-0
    - **Optimized Battery Charging Engaged**: False
    - **Power Source ID**: 21823587
    - **Power Source State**: AC Power
    - **Time to Empty**: 0
    - **Time to Full Charge**: 0
    - **Transport Type**: Internal
    - **Type**: InternalBattery

### Pavel’s MacBook Pro Internal Battery State
- **Entity ID**: `sensor.pavels_macbook_pro_internal_battery_state` | **State**: `Full`
- **Other Attributes**:
    - **Battery Provides Time Remaining**: True
    - **BatteryHealth**: Good
    - **BatteryHealthCondition**: 
    - **Current**: 0
    - **Current Capacity**: 100
    - **DesignCycleCount**: 1000
    - **Hardware Serial Number**: F8YH2V00RPK00000E4
    - **Is Charged**: True
    - **Is Charging**: False
    - **Is Present**: True
    - **LPM Active**: False
    - **Low Power Mode**: False
    - **Max Capacity**: 100
    - **Name**: InternalBattery-0
    - **Optimized Battery Charging Engaged**: False
    - **Power Source ID**: 21823587
    - **Power Source State**: AC Power
    - **Time to Empty**: 0
    - **Time to Full Charge**: 0
    - **Transport Type**: Internal
    - **Type**: InternalBattery

### Pavel’s MacBook Pro Last Update Trigger
- **Entity ID**: `sensor.pavels_macbook_pro_last_update_trigger` | **State**: `Signaled`

### Pavel’s MacBook Pro Location permission
- **Entity ID**: `sensor.pavels_macbook_pro_location_permission` | **State**: `Authorized Always`

### Pavel’s MacBook Pro Primary Display ID
- **Entity ID**: `sensor.pavels_macbook_pro_primary_display_id` | **State**: `F6416D00-479E-47B8-9E25-CC1F359F7AE3`

### Pavel’s MacBook Pro Primary Display Name
- **Entity ID**: `sensor.pavels_macbook_pro_primary_display_name` | **State**: `LS32R75`

### Pavel’s MacBook Pro SSID
- **Entity ID**: `sensor.pavels_macbook_pro_ssid` | **State**: `pap-int`

### Pavel’s MacBook Pro Storage
- **Entity ID**: `sensor.pavels_macbook_pro_storage` | **State**: `26.90`
- **Key Attributes**: unit_of_measurement: % available
- **Other Attributes**:
    - **Available**: 279,89 GB
    - **Available (Important)**: 279,89 GB
    - **Available (Opportunistic)**: 267,54 GB
    - **Total**: 994,66 GB

## Pi {#pi}
*9 entities*

### Pi-Hole Ads blocked today
- **Entity ID**: `sensor.pi_hole_ads_blocked_today` | **State**: `0`
- **Key Attributes**: unit_of_measurement: ads

### Pi-Hole Ads percentage blocked today
- **Entity ID**: `sensor.pi_hole_ads_percentage_blocked_today` | **State**: `0`
- **Key Attributes**: unit_of_measurement: %

### Pi-Hole DNS queries cached
- **Entity ID**: `sensor.pi_hole_dns_queries_cached` | **State**: `9`
- **Key Attributes**: unit_of_measurement: queries

### Pi-Hole DNS queries forwarded
- **Entity ID**: `sensor.pi_hole_dns_queries_forwarded` | **State**: `11`
- **Key Attributes**: unit_of_measurement: queries

### Pi-Hole DNS queries today
- **Entity ID**: `sensor.pi_hole_dns_queries_today` | **State**: `20`
- **Key Attributes**: unit_of_measurement: queries

### Pi-Hole DNS unique clients
- **Entity ID**: `sensor.pi_hole_dns_unique_clients` | **State**: `1`
- **Key Attributes**: unit_of_measurement: clients

### Pi-Hole DNS unique domains
- **Entity ID**: `sensor.pi_hole_dns_unique_domains` | **State**: `1039`
- **Key Attributes**: unit_of_measurement: domains

### Pi-Hole Domains blocked
- **Entity ID**: `sensor.pi_hole_domains_blocked` | **State**: `422472`
- **Key Attributes**: unit_of_measurement: domains

### Pi-Hole Seen clients
- **Entity ID**: `sensor.pi_hole_seen_clients` | **State**: `30`
- **Key Attributes**: unit_of_measurement: clients

## Pocitová teplota meteostanice {#pocitova}
- **Entity ID**: `sensor.pocitova_teplota_meteostanice`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0
    - **unit_of_measurement**: °C

## Power {#power}
*3 entities*

### Solar production forecast Highest power peak time - today
- **Entity ID**: `sensor.power_highest_peak_time_today` | **State**: `2025-06-10T07:00:00+00:00`
- **Key Attributes**: device_class: timestamp

### Solar production forecast Highest power peak time - tomorrow
- **Entity ID**: `sensor.power_highest_peak_time_tomorrow` | **State**: `2025-06-10T07:00:00+00:00`
- **Key Attributes**: device_class: timestamp

### Solar production forecast Estimated power production - now
- **Entity ID**: `sensor.power_production_now` | **State**: `2`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## System Monitor Processor use {#processor}
- **Entity ID**: `sensor.processor_use`
- **State**: `27`
- **Last Changed**: 2025-06-10 07:13:45
- **Last Updated**: 2025-06-10 07:13:45
- **Attributes**:
    - **icon**: mdi:cpu-64-bit
    - **state_class**: measurement
    - **unit_of_measurement**: %

## PV Power {#pv}
- **Entity ID**: `sensor.pv_power`
- **State**: `3957`
- **Last Changed**: 2025-06-10 07:13:53
- **Last Updated**: 2025-06-10 07:13:53
- **Attributes**:
    - **device_class**: power
    - **icon**: mdi:solar-power
    - **state_class**: measurement
    - **unit_of_measurement**: W

## Pv1 {#pv1}
*5 entities*

### PV1 Current
- **Entity ID**: `sensor.pv1_current` | **State**: `8.8`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### PV1 Mode
- **Entity ID**: `sensor.pv1_mode` | **State**: `PV panels connected, producing power`

### PV1 Mode code
- **Entity ID**: `sensor.pv1_mode_code` | **State**: `2`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### PV1 Power
- **Entity ID**: `sensor.pv1_power` | **State**: `3712`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### PV1 Voltage
- **Entity ID**: `sensor.pv1_voltage` | **State**: `421.9`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

## Pv2 {#pv2}
*5 entities*

### PV2 Current
- **Entity ID**: `sensor.pv2_current` | **State**: `1.1`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement

### PV2 Mode
- **Entity ID**: `sensor.pv2_mode` | **State**: `PV panels connected, producing power`

### PV2 Mode code
- **Entity ID**: `sensor.pv2_mode_code` | **State**: `2`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

### PV2 Power
- **Entity ID**: `sensor.pv2_power` | **State**: `245`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### PV2 Voltage
- **Entity ID**: `sensor.pv2_voltage` | **State**: `223.9`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement

## Reolink zahrada Day night state {#reolink}
- **Entity ID**: `sensor.reolink_zahrada_day_night_state`
- **State**: `day`
- **Last Changed**: 2025-06-10 06:59:37
- **Last Updated**: 2025-06-10 06:59:37
- **Attributes**:
    - **device_class**: enum
    - **options**: [
        "day",
        "night",
        "led_day"
      ]

## Req {#req}
*8 entities*

### Požadovaná teplota chodba
- **Entity ID**: `sensor.req_teplota_chodba` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota koupena dole
- **Entity ID**: `sensor.req_teplota_koupelna_dole` | **State**: `13.0`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota koupena nahoře
- **Entity ID**: `sensor.req_teplota_koupelna_nahore` | **State**: `13.0`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota ložnice
- **Entity ID**: `sensor.req_teplota_loznice` | **State**: `20.5`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota obývák
- **Entity ID**: `sensor.req_teplota_obyvaci_pokoj` | **State**: `14.0`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota pokoj dole
- **Entity ID**: `sensor.req_teplota_pokoj_dole` | **State**: `14.5`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota pokoj levý
- **Entity ID**: `sensor.req_teplota_pokoj_levy` | **State**: `20.5`
- **Key Attributes**: unit_of_measurement: °C

### Požadovaná teplota pokoj pravý
- **Entity ID**: `sensor.req_teplota_pokoj_pravy` | **State**: `13.0`
- **Key Attributes**: unit_of_measurement: °C

## RSSI {#rssi}
- **Entity ID**: `sensor.rssi`
- **State**: `101`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Safety {#safety}
*2 entities*

### Safety Country
- **Entity ID**: `sensor.safety_country` | **State**: `CZ-A1`

### Safety Country code
- **Entity ID**: `sensor.safety_country_code` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## Shelly Klimatizace {#shelly-klimatizace}
*2 entities*

### shelly-klimatizace Switch 0 energy
- **Entity ID**: `sensor.shelly_klimatizace_switch_0_energy` | **State**: `97.238169`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### shelly-klimatizace Switch 0 power
- **Entity ID**: `sensor.shelly_klimatizace_switch_0_power` | **State**: `4.6`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## Shellymini Koupelna {#shellymini-koupelna}
*4 entities*

### shellymini-koupelna-nahore-01 energy
- **Entity ID**: `sensor.shellymini_koupelna_nahore_01_energy` | **State**: `263.166583`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### shellymini-koupelna-nahore-01 power
- **Entity ID**: `sensor.shellymini_koupelna_nahore_01_power` | **State**: `0.0`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### shellymini-koupelna-nahore-02 energy
- **Entity ID**: `sensor.shellymini_koupelna_nahore_02_energy` | **State**: `517.953065`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### shellymini-koupelna-nahore-02 power
- **Entity ID**: `sensor.shellymini_koupelna_nahore_02_power` | **State**: `0.0`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## Sonoff 4Ch01 {#sonoff-4ch01}
*5 entities*

### Tasmota Last Restart Time
- **Entity ID**: `sensor.sonoff_4ch01_1_last_restart_time` | **State**: `2025-05-28T17:11:30+00:00`
- **Key Attributes**: device_class: timestamp

### Tasmota MQTT Connect Count
- **Entity ID**: `sensor.sonoff_4ch01_1_mqtt_connect_count` | **State**: `5`

### Tasmota Restart Reason
- **Entity ID**: `sensor.sonoff_4ch01_1_restart_reason` | **State**: `Power On`

### Tasmota SSID
- **Entity ID**: `sensor.sonoff_4ch01_1_ssid` | **State**: `pap-int`

### Tasmota WiFi Connect Count
- **Entity ID**: `sensor.sonoff_4ch01_1_wifi_connect_count` | **State**: `1`

## Sonoff Energy {#sonoff-energy}
*20 entities*

### sensor.sonoff_energy_apparentpower_2
- **Entity ID**: `sensor.sonoff_energy_apparentpower_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY ApparentPower
- **Entity ID**: `sensor.sonoff_energy_apparentpower_3` | **State**: `unavailable`
- **Key Attributes**: device_class: apparent_power, unit_of_measurement: VA, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_current_2
- **Entity ID**: `sensor.sonoff_energy_current_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Current
- **Entity ID**: `sensor.sonoff_energy_current_3` | **State**: `unavailable`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_factor_2
- **Entity ID**: `sensor.sonoff_energy_factor_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Factor
- **Entity ID**: `sensor.sonoff_energy_factor_3` | **State**: `unavailable`
- **Key Attributes**: device_class: power_factor, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_power_2
- **Entity ID**: `sensor.sonoff_energy_power_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Power
- **Entity ID**: `sensor.sonoff_energy_power_3` | **State**: `unavailable`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_reactivepower_2
- **Entity ID**: `sensor.sonoff_energy_reactivepower_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY ReactivePower
- **Entity ID**: `sensor.sonoff_energy_reactivepower_3` | **State**: `unavailable`
- **Key Attributes**: device_class: reactive_power, unit_of_measurement: var, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_today_2
- **Entity ID**: `sensor.sonoff_energy_today_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Today
- **Entity ID**: `sensor.sonoff_energy_today_3` | **State**: `unavailable`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_total_2
- **Entity ID**: `sensor.sonoff_energy_total_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Total
- **Entity ID**: `sensor.sonoff_energy_total_3` | **State**: `unavailable`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_totalstarttime_2
- **Entity ID**: `sensor.sonoff_energy_totalstarttime_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY TotalStartTime
- **Entity ID**: `sensor.sonoff_energy_totalstarttime_3` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_voltage_2
- **Entity ID**: `sensor.sonoff_energy_voltage_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Voltage
- **Entity ID**: `sensor.sonoff_energy_voltage_3` | **State**: `unavailable`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### sensor.sonoff_energy_yesterday_2
- **Entity ID**: `sensor.sonoff_energy_yesterday_2` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### ENERGY Yesterday
- **Entity ID**: `sensor.sonoff_energy_yesterday_3` | **State**: `unavailable`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Last Restart Time {#sonoff-last}
- **Entity ID**: `sensor.sonoff_last_restart_time`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: timestamp
    - **restored**: True
    - **supported_features**: 0

## MQTT Connect Count {#sonoff-mqtt}
- **Entity ID**: `sensor.sonoff_mqtt_connect_count`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **icon**: mdi:counter
    - **restored**: True
    - **supported_features**: 0

## Restart Reason {#sonoff-restart}
- **Entity ID**: `sensor.sonoff_restart_reason`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **icon**: mdi:information-outline
    - **restored**: True
    - **supported_features**: 0

## SSID {#sonoff-ssid}
- **Entity ID**: `sensor.sonoff_ssid`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **icon**: mdi:access-point-network
    - **restored**: True
    - **supported_features**: 0

## WiFi Connect Count {#sonoff-wifi}
- **Entity ID**: `sensor.sonoff_wifi_connect_count`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **icon**: mdi:counter
    - **restored**: True
    - **supported_features**: 0

## sensor.sonoffpow02_status {#sonoffpow02}
- **Entity ID**: `sensor.sonoffpow02_status`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Sun {#sun}
*6 entities*

### Sun Next dawn
- **Entity ID**: `sensor.sun_next_dawn` | **State**: `2025-06-11T02:08:17+00:00`
- **Key Attributes**: device_class: timestamp

### Sun Next dusk
- **Entity ID**: `sensor.sun_next_dusk` | **State**: `2025-06-10T19:54:28+00:00`
- **Key Attributes**: device_class: timestamp

### Sun Next midnight
- **Entity ID**: `sensor.sun_next_midnight` | **State**: `2025-06-10T23:01:34+00:00`
- **Key Attributes**: device_class: timestamp

### Sun Next noon
- **Entity ID**: `sensor.sun_next_noon` | **State**: `2025-06-10T11:01:15+00:00`
- **Key Attributes**: device_class: timestamp

### Sun Next rising
- **Entity ID**: `sensor.sun_next_rising` | **State**: `2025-06-11T02:53:47+00:00`
- **Key Attributes**: device_class: timestamp

### Sun Next setting
- **Entity ID**: `sensor.sun_next_setting` | **State**: `2025-06-10T19:08:57+00:00`
- **Key Attributes**: device_class: timestamp

## Synology {#synology}
*24 entities*

### Synology CPU load average (15 min)
- **Entity ID**: `sensor.synology_cpu_load_average_15_min` | **State**: `0.34`
- **Key Attributes**: unit_of_measurement: load
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology CPU load average (5 min)
- **Entity ID**: `sensor.synology_cpu_load_average_5_min` | **State**: `0.24`
- **Key Attributes**: unit_of_measurement: load
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology CPU utilization (total)
- **Entity ID**: `sensor.synology_cpu_utilization_total` | **State**: `19`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology CPU utilization (user)
- **Entity ID**: `sensor.synology_cpu_utilization_user` | **State**: `16`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Download throughput
- **Entity ID**: `sensor.synology_download_throughput` | **State**: `208.254`
- **Key Attributes**: device_class: data_rate, unit_of_measurement: kB/s, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 1) Status
- **Entity ID**: `sensor.synology_drive_1_status` | **State**: `normal`
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 1) Temperature
- **Entity ID**: `sensor.synology_drive_1_temperature` | **State**: `40`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 2) Status
- **Entity ID**: `sensor.synology_drive_2_status` | **State**: `normal`
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Drive 2) Temperature
- **Entity ID**: `sensor.synology_drive_2_temperature` | **State**: `42`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Memory available (real)
- **Entity ID**: `sensor.synology_memory_available_real` | **State**: `321.26976`
- **Key Attributes**: device_class: data_size, unit_of_measurement: MB, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Memory available (swap)
- **Entity ID**: `sensor.synology_memory_available_swap` | **State**: `2087.886848`
- **Key Attributes**: device_class: data_size, unit_of_measurement: MB, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Memory total (real)
- **Entity ID**: `sensor.synology_memory_total_real` | **State**: `6067.13856`
- **Key Attributes**: device_class: data_size, unit_of_measurement: MB, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Memory total (swap)
- **Entity ID**: `sensor.synology_memory_total_swap` | **State**: `5788.061696`
- **Key Attributes**: device_class: data_size, unit_of_measurement: MB, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Memory usage (real)
- **Entity ID**: `sensor.synology_memory_usage_real` | **State**: `43`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Temperature
- **Entity ID**: `sensor.synology_temperature` | **State**: `46`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology Upload throughput
- **Entity ID**: `sensor.synology_upload_throughput` | **State**: `10.285`
- **Key Attributes**: device_class: data_rate, unit_of_measurement: kB/s, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Average disk temp
- **Entity ID**: `sensor.synology_volume_1_average_disk_temp` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Status
- **Entity ID**: `sensor.synology_volume_1_status` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Used space
- **Entity ID**: `sensor.synology_volume_1_used_space` | **State**: `unavailable`
- **Key Attributes**: device_class: data_size, unit_of_measurement: TB, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Volume used
- **Entity ID**: `sensor.synology_volume_1_volume_used` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Synology (Volume 2) Average disk temp
- **Entity ID**: `sensor.synology_volume_2_average_disk_temp` | **State**: `41.0`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Volume 2) Status
- **Entity ID**: `sensor.synology_volume_2_status` | **State**: `normal`
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Volume 2) Used space
- **Entity ID**: `sensor.synology_volume_2_used_space` | **State**: `6.210018369536`
- **Key Attributes**: device_class: data_size, unit_of_measurement: TB, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Synology

### Synology (Volume 2) Volume used
- **Entity ID**: `sensor.synology_volume_2_volume_used` | **State**: `54.0`
- **Key Attributes**: unit_of_measurement: %
- **Other Attributes**:
    - **attribution**: Data provided by Synology

## Tb350Fu {#tb350fu}
*6 entities*

### TB350FU Battery level
- **Entity ID**: `sensor.tb350fu_battery_level` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### TB350FU Battery level
- **Entity ID**: `sensor.tb350fu_battery_level_2` | **State**: `74`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement

### TB350FU Battery state
- **Entity ID**: `sensor.tb350fu_battery_state` | **State**: `charging`
- **Key Attributes**: device_class: enum
- **Other Attributes**:
    - **options**: [
        "charging",
        "discharging",
        "full",
        "not_charging"
      ]

### TB350FU Battery state
- **Entity ID**: `sensor.tb350fu_battery_state_2` | **State**: `discharging`
- **Key Attributes**: device_class: enum
- **Other Attributes**:
    - **options**: [
        "charging",
        "discharging",
        "full",
        "not_charging"
      ]

### TB350FU Charger type
- **Entity ID**: `sensor.tb350fu_charger_type` | **State**: `ac`
- **Key Attributes**: device_class: enum
- **Other Attributes**:
    - **options**: [
        "ac",
        "usb",
        "wireless",
        "dock",
        "none"
      ]

### TB350FU Charger type
- **Entity ID**: `sensor.tb350fu_charger_type_2` | **State**: `none`
- **Key Attributes**: device_class: enum
- **Other Attributes**:
    - **options**: [
        "ac",
        "usb",
        "wireless",
        "dock",
        "none"
      ]

## Teplota {#teplota}
*11 entities*

### Teplota chodba
- **Entity ID**: `sensor.teplota_chodba` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: °C

### Teplota koupena dole
- **Entity ID**: `sensor.teplota_koupelna_dole` | **State**: `21.5`
- **Key Attributes**: unit_of_measurement: °C

### Teplota koupena nahoře
- **Entity ID**: `sensor.teplota_koupelna_nahore` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: °C

### Teplota ložnice
- **Entity ID**: `sensor.teplota_loznice` | **State**: `23.0`
- **Key Attributes**: unit_of_measurement: °C

### Teplota obývací pokoj
- **Entity ID**: `sensor.teplota_obyvaci_pokoj` | **State**: `23.1`
- **Key Attributes**: unit_of_measurement: °C

### Teplota pokoj dole
- **Entity ID**: `sensor.teplota_pokoj_dole` | **State**: `22.1`
- **Key Attributes**: unit_of_measurement: °C

### Teplota pokoj levý
- **Entity ID**: `sensor.teplota_pokoj_levy` | **State**: `22.9`
- **Key Attributes**: unit_of_measurement: °C

### Teplota pokoj pravý
- **Entity ID**: `sensor.teplota_pokoj_pravy` | **State**: `23.5`
- **Key Attributes**: unit_of_measurement: °C

### Teplota vody v bazenu
- **Entity ID**: `sensor.teplota_vody_v_bazenu` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Teplota vody v bazenu
- **Entity ID**: `sensor.teplota_vody_v_bazenu_2` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### testpavel Teplota vody v bazenu
- **Entity ID**: `sensor.teplota_vody_v_bazenu_3` | **State**: `unavailable`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement

## Timestamp {#timestamp}
- **Entity ID**: `sensor.timestamp`
- **State**: `2025-06-10 09:11:33`
- **Last Changed**: 2025-06-10 07:13:53
- **Last Updated**: 2025-06-10 07:13:53

## Today {#today}
*6 entities*

### Today Battery Charge
- **Entity ID**: `sensor.today_battery_charge` | **State**: `2.7`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Today Battery Discharge
- **Entity ID**: `sensor.today_battery_discharge` | **State**: `0.0`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Today Energy (export)
- **Entity ID**: `sensor.today_energy_export` | **State**: `7.5`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Today Energy (import)
- **Entity ID**: `sensor.today_energy_import` | **State**: `0.0`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Today Load
- **Entity ID**: `sensor.today_load` | **State**: `2.9`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Today's PV Generation
- **Entity ID**: `sensor.today_s_pv_generation` | **State**: `8.8`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

## Total {#total}
*7 entities*

### Total Battery Charge
- **Entity ID**: `sensor.total_battery_charge` | **State**: `3628.1`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Total Battery Discharge
- **Entity ID**: `sensor.total_battery_discharge` | **State**: `1933.8`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Total Energy (export)
- **Entity ID**: `sensor.total_energy_export` | **State**: `12514.7`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Total Energy (import)
- **Entity ID**: `sensor.total_energy_import` | **State**: `2.6`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Total Load
- **Entity ID**: `sensor.total_load` | **State**: `7740.7`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### Total Power
- **Entity ID**: `sensor.total_power` | **State**: `3883`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### Total PV Generation
- **Entity ID**: `sensor.total_pv_generation` | **State**: `12717.9`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

## Ulice {#ulice}
*4 entities*

### Ulice All Active Count
- **Entity ID**: `sensor.ulice_all_active_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Ulice All count
- **Entity ID**: `sensor.ulice_all_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Ulice Person Active Count
- **Entity ID**: `sensor.ulice_person_active_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Ulice Person count
- **Entity ID**: `sensor.ulice_person_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

## Ups {#ups}
*9 entities*

### Ups Alarms
- **Entity ID**: `sensor.ups_alarms` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Battery Charge
- **Entity ID**: `sensor.ups_battery_charge` | **State**: `unavailable`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Battery Runtime
- **Entity ID**: `sensor.ups_battery_runtime` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: s
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Battery Voltage
- **Entity ID**: `sensor.ups_battery_voltage` | **State**: `unavailable`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Input Voltage
- **Entity ID**: `sensor.ups_input_voltage` | **State**: `unavailable`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Load
- **Entity ID**: `sensor.ups_load` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Load
- **Entity ID**: `sensor.ups_load_2` | **State**: `1`
- **Key Attributes**: unit_of_measurement: %, state_class: measurement

### Ups Low Battery Runtime
- **Entity ID**: `sensor.ups_low_battery_runtime` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: s
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Ups Status Data
- **Entity ID**: `sensor.ups_status_data` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Venkovni {#venkovni}
*2 entities*

### Venkovní teplota meteostanice
- **Entity ID**: `sensor.venkovni_teplota_meteostanice` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Venkovní vlhkost vzduchu
- **Entity ID**: `sensor.venkovni_vlhkost_vzduchu` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: %
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Version {#version}
*2 entities*

### version_available
- **Entity ID**: `sensor.version_available` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### version_available
- **Entity ID**: `sensor.version_available_2` | **State**: `2025.5.3`
- **Other Attributes**:
    - **audio**: 2025.02.0
    - **board**: ova
    - **channel**: stable
    - **cli**: 2025.04.0
    - **dns**: 2025.02.0
    - **image**: default
    - **multicast**: 2025.02.0
    - **observer**: 2025.02.0
    - **os**: 15.2
    - **source**: supervisor
    - **supervisor**: 2025.05.3

## Vlhkost vzduchu v obývacím pokoji {#vlhkost}
- **Entity ID**: `sensor.vlhkost_vzduchu_v_obyvacim_pokoji`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0
    - **unit_of_measurement**: %

## Vlhkostni {#vlhkostni}
*3 entities*

### Vlhkostni cidlo
- **Entity ID**: `sensor.vlhkostni_cidlo` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Vlhkostni cidlo
- **Entity ID**: `sensor.vlhkostni_cidlo_2` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### testpavel Vlhkostni cidlo
- **Entity ID**: `sensor.vlhkostni_cidlo_3` | **State**: `unavailable`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement

## Vnitrni {#vnitrni}
*2 entities*

### sensor.vnitrni_teplota_meteostanice
- **Entity ID**: `sensor.vnitrni_teplota_meteostanice` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Vnitřní teplota meteostanice
- **Entity ID**: `sensor.vnitrni_teplota_meteostanice_2` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: °C
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Vysavac {#vysavac}
*9 entities*

### Vysavač horní patro Current clean area
- **Entity ID**: `sensor.vysavac_horni_patro_current_clean_area` | **State**: `25.625`
- **Key Attributes**: unit_of_measurement: m²

### Vysavač horní patro Current clean duration
- **Entity ID**: `sensor.vysavac_horni_patro_current_clean_duration` | **State**: `2025`
- **Key Attributes**: device_class: duration, unit_of_measurement: s

### Vysavač horní patro Filter remaining
- **Entity ID**: `sensor.vysavac_horni_patro_filter_remaining` | **State**: `521398`
- **Key Attributes**: device_class: duration, unit_of_measurement: s

### Vysavač horní patro Last clean area
- **Entity ID**: `sensor.vysavac_horni_patro_last_clean_area` | **State**: `25.625`
- **Key Attributes**: unit_of_measurement: m²

### Vysavač horní patro Last clean duration
- **Entity ID**: `sensor.vysavac_horni_patro_last_clean_duration` | **State**: `2025`
- **Key Attributes**: device_class: duration, unit_of_measurement: s

### Vysavač horní patro Last clean end
- **Entity ID**: `sensor.vysavac_horni_patro_last_clean_end` | **State**: `2025-06-09T09:23:59+00:00`
- **Key Attributes**: device_class: timestamp

### Vysavač horní patro Last clean start
- **Entity ID**: `sensor.vysavac_horni_patro_last_clean_start` | **State**: `2025-06-09T08:50:14+00:00`
- **Key Attributes**: device_class: timestamp

### Vysavač horní patro Main brush remaining
- **Entity ID**: `sensor.vysavac_horni_patro_main_brush_remaining` | **State**: `957734`
- **Key Attributes**: device_class: duration, unit_of_measurement: s

### Vysavač horní patro Side brush remaining
- **Entity ID**: `sensor.vysavac_horni_patro_side_brush_remaining` | **State**: `597734`
- **Key Attributes**: device_class: duration, unit_of_measurement: s

## Vysavač horní patro Sensor dirty remaining {#vysavac-horni-patro}
- **Entity ID**: `sensor.vysavac_horni_patro_sensor_dirty_remaining`
- **State**: `-14266`
- **Last Changed**: 2025-06-10 06:24:04
- **Last Updated**: 2025-06-10 06:24:04
- **Attributes**:
    - **device_class**: duration
    - **icon**: mdi:eye-outline
    - **unit_of_measurement**: s

## Warning code {#warning}
- **Entity ID**: `sensor.warning_code`
- **State**: `0`
- **Last Changed**: 2025-06-08 19:41:46
- **Last Updated**: 2025-06-08 19:41:46
- **Attributes**:
    - **state_class**: measurement
    - **unit_of_measurement**: 

## Weather Station Weather Station Weather Station Loznice Netatmo {#weather-station-weather-station-weather-station-loznice-netatmo}
*4 entities*

### Weather Station Ložnice Netatmo Sensor Battery
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_battery_percent` | **State**: `18`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Ložnice Netatmo Sensor Carbon dioxide
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2` | **State**: `543`
- **Key Attributes**: device_class: carbon_dioxide, unit_of_measurement: ppm, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Ložnice Netatmo Sensor Humidity
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_humidity` | **State**: `51`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Ložnice Netatmo Sensor Temperature
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_temperature` | **State**: `22`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

## Weather Station Weather Station Weather Station Pracovna Netatmo {#weather-station-weather-station-weather-station-pracovna-netatmo}
*4 entities*

### Weather Station Pracovna Netatmo Sensor Battery
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_battery_percent` | **State**: `12`
- **Key Attributes**: device_class: battery, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Pracovna Netatmo Sensor Carbon dioxide
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2` | **State**: `450`
- **Key Attributes**: device_class: carbon_dioxide, unit_of_measurement: ppm, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Pracovna Netatmo Sensor Humidity
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_humidity` | **State**: `46`
- **Key Attributes**: device_class: humidity, unit_of_measurement: %, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

### Weather Station Pracovna Netatmo Sensor Temperature
- **Entity ID**: `sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_temperature` | **State**: `22.3`
- **Key Attributes**: device_class: temperature, unit_of_measurement: °C, state_class: measurement
- **Other Attributes**:
    - **attribution**: Data provided by Netatmo

## Wifi {#wifi}
*2 entities*

### WiFi Signal Sensor
- **Entity ID**: `sensor.wifi_signal_sensor` | **State**: `unavailable`
- **Key Attributes**: device_class: signal_strength, unit_of_measurement: dBm, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Wifi teploměr
- **Entity ID**: `sensor.wifi_teplomer` | **State**: `unknown`
- **Key Attributes**: unit_of_measurement: °C

## Wifi Signal {#wifi-signal}
*2 entities*

### WiFi Signal Sensor
- **Entity ID**: `sensor.wifi_signal_sensor_2` | **State**: `unavailable`
- **Key Attributes**: device_class: signal_strength, unit_of_measurement: dBm, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### testpavel WiFi Signal Sensor
- **Entity ID**: `sensor.wifi_signal_sensor_4` | **State**: `unavailable`
- **Key Attributes**: device_class: signal_strength, unit_of_measurement: dBm, state_class: measurement

## Work {#work}
*2 entities*

### Work Mode
- **Entity ID**: `sensor.work_mode` | **State**: `Normal (On-Grid)`

### Work Mode code
- **Entity ID**: `sensor.work_mode_code` | **State**: `1`
- **Key Attributes**: unit_of_measurement: , state_class: measurement

## Xiaomi Robot Vacuum X10 {#xiaomi-robot-vacuum-x10}
*19 entities*

### Xiaomi Robot Vacuum X10+ Auto Empty Status
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_auto_empty_status` | **State**: `idle`
- **Key Attributes**: device_class: dreame_vacuum__auto_empty_status
- **Other Attributes**:
    - **value**: 0

### Xiaomi Robot Vacuum X10+ Battery Level
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_battery_level` | **State**: `100`
- **Key Attributes**: device_class: battery, unit_of_measurement: %

### Xiaomi Robot Vacuum X10+ Charging Status
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_charging_status` | **State**: `charging_completed`
- **Key Attributes**: device_class: dreame_vacuum__charging_status
- **Other Attributes**:
    - **value**: 1

### Xiaomi Robot Vacuum X10+ Cleaned Area
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_cleaned_area` | **State**: `18`
- **Key Attributes**: unit_of_measurement: m²

### Xiaomi Robot Vacuum X10+ Cleaning Count
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_cleaning_count` | **State**: `746`
- **Key Attributes**: unit_of_measurement: x

### Xiaomi Robot Vacuum X10+ Cleaning History
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_cleaning_history` | **State**: `2025-06-09T21:29:45+00:00`
- **Key Attributes**: device_class: timestamp
- **Other Attributes**:
    - **2025-05-27 23:05:10**: {
        "cleaning_time": "28 min",
        "cleaned_area": "24 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-05-28 13:24:58**: {
        "cleaning_time": "30 min",
        "cleaned_area": "27 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-05-29 11:55:37**: {
        "cleaning_time": "43 min",
        "cleaned_area": "28 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-05-30 16:40:22**: {
        "cleaning_time": "41 min",
        "cleaned_area": "33 m²",
        "status": "Cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-05-31 00:02:11**: {
        "cleaning_time": "20 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-05-31 22:49:09**: {
        "cleaning_time": "21 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-01 07:49:39**: {
        "cleaning_time": "13 min",
        "cleaned_area": "10 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-01 22:29:46**: {
        "cleaning_time": "20 min",
        "cleaned_area": "17 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-02 14:27:07**: {
        "cleaning_time": "33 min",
        "cleaned_area": "26 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-03 08:12:53**: {
        "cleaning_time": "32 min",
        "cleaned_area": "27 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-03 23:13:19**: {
        "cleaning_time": "21 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-04 12:46:54**: {
        "cleaning_time": "33 min",
        "cleaned_area": "28 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-04 19:48:42**: {
        "cleaning_time": "38 min",
        "cleaned_area": "33 m²",
        "status": "Cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-07 00:29:30**: {
        "cleaning_time": "19 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-07 15:27:11**: {
        "cleaning_time": "39 min",
        "cleaned_area": "32 m²",
        "status": "Cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-07 16:45:08**: {
        "cleaning_time": "12 min",
        "cleaned_area": "9 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-08 01:54:45**: {
        "cleaning_time": "20 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-08 14:24:11**: {
        "cleaning_time": "19 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-09 07:59:01**: {
        "cleaning_time": "43 min",
        "cleaned_area": "33 m²",
        "status": "Cleaning",
        "completed": true,
        "water_tank": "Installed"
      }
    - **2025-06-09 23:29:45**: {
        "cleaning_time": "17 min",
        "cleaned_area": "18 m²",
        "status": "Room cleaning",
        "completed": true,
        "water_tank": "Installed"
      }

### Xiaomi Robot Vacuum X10+ Cleaning Time
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_cleaning_time` | **State**: `17`
- **Key Attributes**: unit_of_measurement: min

### Xiaomi Robot Vacuum X10+ Current Room
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_current_room` | **State**: `Living room`
- **Other Attributes**:
    - **room_icon**: mdi:home-outline
    - **room_id**: 4

### Xiaomi Robot Vacuum X10+ Dust Collection
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_dust_collection` | **State**: `available`
- **Key Attributes**: device_class: dreame_vacuum__dust_collection
- **Other Attributes**:
    - **value**: 1

### Xiaomi Robot Vacuum X10+ Error
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_error` | **State**: `no_error`
- **Key Attributes**: device_class: dreame_vacuum__error
- **Other Attributes**:
    - **value**: 0

### Xiaomi Robot Vacuum X10+ Mapping Time
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_mapping_time` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: min

### Xiaomi Robot Vacuum X10+ Mop Pad
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_mop_pad` | **State**: `installed`
- **Key Attributes**: device_class: dreame_vacuum__water_tank_and_mop

### Xiaomi Robot Vacuum X10+ Relocation Status
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_relocation_status` | **State**: `located`
- **Key Attributes**: device_class: dreame_vacuum__relocation_status
- **Other Attributes**:
    - **value**: 0

### Xiaomi Robot Vacuum X10+ Self-Wash Base Status
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_self_wash_base_status` | **State**: `idle`
- **Key Attributes**: device_class: dreame_vacuum__self_wash_base_status
- **Other Attributes**:
    - **value**: 0

### Xiaomi Robot Vacuum X10+ State
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_state` | **State**: `charging_completed`
- **Key Attributes**: device_class: dreame_vacuum__state
- **Other Attributes**:
    - **value**: 13

### Xiaomi Robot Vacuum X10+ Status
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_status` | **State**: `sleeping`
- **Key Attributes**: device_class: dreame_vacuum__status
- **Other Attributes**:
    - **value**: 14

### Xiaomi Robot Vacuum X10+ Task Status
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_task_status` | **State**: `completed`
- **Key Attributes**: device_class: dreame_vacuum__task_status
- **Other Attributes**:
    - **value**: 0

### Xiaomi Robot Vacuum X10+ Total Cleaned Area
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_total_cleaned_area` | **State**: `17072`
- **Key Attributes**: unit_of_measurement: m²

### Xiaomi Robot Vacuum X10+ Total Cleaning Time
- **Entity ID**: `sensor.xiaomi_robot_vacuum_x10_total_cleaning_time` | **State**: `19432`
- **Key Attributes**: device_class: duration, unit_of_measurement: min

## Zahrada {#zahrada}
*8 entities*

### Zahrada All Active Count
- **Entity ID**: `sensor.zahrada_all_active_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada All count
- **Entity ID**: `sensor.zahrada_all_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada-Bouda All Active Count
- **Entity ID**: `sensor.zahrada_bouda_all_active_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada-Bouda All count
- **Entity ID**: `sensor.zahrada_bouda_all_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada-Bouda Person Active Count
- **Entity ID**: `sensor.zahrada_bouda_person_active_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada-Bouda Person count
- **Entity ID**: `sensor.zahrada_bouda_person_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada Person Active Count
- **Entity ID**: `sensor.zahrada_person_active_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

### Zahrada Person count
- **Entity ID**: `sensor.zahrada_person_count` | **State**: `0`
- **Key Attributes**: unit_of_measurement: objects

## Zasuvka {#zasuvka}
*10 entities*

### Current
- **Entity ID**: `sensor.zasuvka_current` | **State**: `unavailable`
- **Key Attributes**: device_class: current, unit_of_measurement: A, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Current consumption
- **Entity ID**: `sensor.zasuvka_current_consumption` | **State**: `unavailable`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Zásuvka dnes celkem
- **Entity ID**: `sensor.zasuvka_dnes_celkem` | **State**: `0.0`
- **Key Attributes**: device_class: duration, unit_of_measurement: h, state_class: measurement

### Today's Consumption
- **Entity ID**: `sensor.zasuvka_today_kwh` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: kWh

### Today's consumption
- **Entity ID**: `sensor.zasuvka_today_s_consumption` | **State**: `unavailable`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Total consumption
- **Entity ID**: `sensor.zasuvka_total_consumption` | **State**: `unavailable`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Total Consumption
- **Entity ID**: `sensor.zasuvka_total_kwh` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: kWh

### Voltage
- **Entity ID**: `sensor.zasuvka_voltage` | **State**: `unavailable`
- **Key Attributes**: device_class: voltage, unit_of_measurement: V, state_class: measurement
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Voltage
- **Entity ID**: `sensor.zasuvka_volts` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: V

### Current Consumption
- **Entity ID**: `sensor.zasuvka_watts` | **State**: `unavailable`
- **Key Attributes**: unit_of_measurement: W

## Zasuvky Technicka Mistnost {#zasuvky-technicka-mistnost}
*4 entities*

### zasuvky-technicka-mistnost switch_0 energy
- **Entity ID**: `sensor.zasuvky_technicka_mistnost_switch_0_energy` | **State**: `2.884751`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### zasuvky-technicka-mistnost switch_0 power
- **Entity ID**: `sensor.zasuvky_technicka_mistnost_switch_0_power` | **State**: `1.1`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

### zasuvky-technicka-mistnost switch_1 energy
- **Entity ID**: `sensor.zasuvky_technicka_mistnost_switch_1_energy` | **State**: `118.153633`
- **Key Attributes**: device_class: energy, unit_of_measurement: kWh, state_class: total_increasing

### zasuvky-technicka-mistnost switch_1 power
- **Entity ID**: `sensor.zasuvky_technicka_mistnost_switch_1_power` | **State**: `15.6`
- **Key Attributes**: device_class: power, unit_of_measurement: W, state_class: measurement

## Zigbee2Mqtt {#zigbee2mqtt}
*2 entities*

### Permit join timeout
- **Entity ID**: `sensor.zigbee2mqtt_bridge_permit_join_timeout` | **State**: `unavailable`
- **Key Attributes**: device_class: duration, unit_of_measurement: s
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Zigbee2MQTT Bridge Version
- **Entity ID**: `sensor.zigbee2mqtt_bridge_version` | **State**: `2.3.0`
