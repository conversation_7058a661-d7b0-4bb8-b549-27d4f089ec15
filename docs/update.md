# Update Entities

[← Back to Overview](./index.md)

*Total entities: 38*

## Table of Contents

- [0X000D6Ffffefc30F1](#0x000d6ffffefc30f1)
- [0X0017880104F1247D](#0x0017880104f1247d)
- [0X001788010C6Cdbaf](#0x001788010c6cdbaf)
- [0X0C4314Fffe5Fe450](#0x0c4314fffe5fe450)
- [0X7Cb03Eaa00B25De1](#0x7cb03eaa00b25de1)
- [0X84182600000Da73E](#0x84182600000da73e)
- [0X8418260000105301](#0x8418260000105301)
- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [0Xa4C13887Ab4A50C0](#0xa4c13887ab4a50c0)
- [Catppuccin](#catppuccin)
- [Caule](#caule)
- [Dream<PERSON>](#dreame)
- [Frigate](#frigate)
- [Hacs](#hacs)
- [Horizon](#horizon)
- [Ios](#ios)
- [Metrology](#metrology)
- [Midnight](#midnight)
- [Mini](#mini)
- [Mushroom](#mushroom)
- [Pi](#pi)
- [Power](#power)
- [Reolink](#reolink)
- [Sankey](#sankey)
- [Sensor](#sensor)
- [Shelly Klimatizace](#shelly-klimatizace)
- [Shellymini Koupelna](#shellymini-koupelna)
- [Smartthinq](#smartthinq)
- [Synology](#synology)
- [Synthwave](#synthwave)
- [Vacuum](#vacuum)
- [Zasuvka](#zasuvka)
- [Zasuvky](#zasuvky)

## 0x000d6ffffefc30f1 {#0x000d6ffffefc30f1}
- **Entity ID**: `update.0x000d6ffffefc30f1`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **device_class**: firmware
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **supported_features**: 5

## 0x0017880104f1247d {#0x0017880104f1247d}
- **Entity ID**: `update.0x0017880104f1247d`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **device_class**: firmware
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **supported_features**: 5

## 0x001788010c6cdbaf {#0x001788010c6cdbaf}
- **Entity ID**: `update.0x001788010c6cdbaf`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **in_progress**: False
    - **installed_version**: 16786434
    - **latest_version**: 16786434
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None

## Tesla teplotni cidlo s displejem {#0x0c4314fffe5fe450}
- **Entity ID**: `update.0x0c4314fffe5fe450`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **in_progress**: False
    - **installed_version**: -1
    - **latest_version**: -1
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None

## Zasuvka CUBE (Osram) {#0x7cb03eaa00b25de1}
- **Entity ID**: `update.0x7cb03eaa00b25de1`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **device_class**: firmware
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **supported_features**: 5

## 0x84182600000da73e {#0x84182600000da73e}
- **Entity ID**: `update.0x84182600000da73e`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **in_progress**: False
    - **installed_version**: 16909577
    - **latest_version**: 16909577
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None

## Zasuvka Adelka pokoj {#0x8418260000105301}
- **Entity ID**: `update.0x8418260000105301`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50
- **Attributes**:
    - **device_class**: firmware
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **supported_features**: 5

## 0x84fd27fffea36224 {#0x84fd27fffea36224}
- **Entity ID**: `update.0x84fd27fffea36224`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **in_progress**: False
    - **installed_version**: -1
    - **latest_version**: -1
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None

## 0xa4c13887ab4a50c0 {#0xa4c13887ab4a50c0}
- **Entity ID**: `update.0xa4c13887ab4a50c0`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://github.com/Koenkk/zigbee2mqtt/raw/master/images/logo.png
    - **in_progress**: False
    - **installed_version**: -1
    - **latest_version**: -1
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None

## Catppuccin Theme update {#catppuccin}
- **Entity ID**: `update.catppuccin_theme_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v1.0.2
    - **latest_version**: v1.0.2
    - **release_summary**: None
    - **release_url**: https://github.com/catppuccin/home-assistant/releases/v1.0.2
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Caule Themes Pack 1 - by caule.studio update {#caule}
- **Entity ID**: `update.caule_themes_pack_1_by_caule_studio_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v.1.3.3
    - **latest_version**: v.1.3.3
    - **release_summary**: None
    - **release_url**: https://github.com/ricardoquecria/caule-themes-pack-1/releases/v.1.3.3
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Dreame Vacuum update {#dreame}
- **Entity ID**: `update.dreame_vacuum_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/dreame_vacuum/icon.png
    - **in_progress**: False
    - **installed_version**: v1.0.5
    - **latest_version**: v1.0.5
    - **release_summary**: None
    - **release_url**: https://github.com/Tasshack/dreame-vacuum/releases/v1.0.5
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Frigate {#frigate}
*3 entities*

### Advanced Camera Card update
- **Entity ID**: `update.frigate_card_update` | **State**: `off`
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v7.14.1
    - **latest_version**: v7.14.1
    - **release_url**: https://github.com/dermotduffy/advanced-camera-card/releases/v7.14.1
    - **supported_features**: 23

### Frigate Server
- **Entity ID**: `update.frigate_server` | **State**: `on`
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/frigate/icon.png
    - **in_progress**: False
    - **installed_version**: 0.15.0
    - **latest_version**: 0.15.1
    - **release_url**: https://github.com/blakeblackshear/frigate/releases/tag/v0.15.1
    - **supported_features**: 0

### Frigate update
- **Entity ID**: `update.frigate_update` | **State**: `off`
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/frigate/icon.png
    - **in_progress**: False
    - **installed_version**: v5.9.2
    - **latest_version**: v5.9.2
    - **release_summary**: <ha-alert alert-type='error'>Restart of Home Assistant required</ha-alert>
    - **release_url**: https://github.com/blakeblackshear/frigate-hass-integration/releases/v5.9.2
    - **supported_features**: 23

## HACS update {#hacs}
- **Entity ID**: `update.hacs_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/hacs/icon.png
    - **in_progress**: False
    - **installed_version**: 2.0.5
    - **latest_version**: 2.0.5
    - **release_summary**: None
    - **release_url**: https://github.com/hacs/integration/releases/2.0.5
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Horizon Card update {#horizon}
- **Entity ID**: `update.horizon_card_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:45:57
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v1.3.1
    - **latest_version**: v1.3.1
    - **release_summary**: None
    - **release_url**: https://github.com/rejuvenate/lovelace-horizon-card/releases/v1.3.1
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## iOS Themes - Dark Mode and Light Mode update {#ios}
- **Entity ID**: `update.ios_themes_dark_mode_and_light_mode_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v3.0.1
    - **latest_version**: v3.0.1
    - **release_summary**: None
    - **release_url**: https://github.com/basnijholt/lovelace-ios-themes/releases/v3.0.1
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Metrology - Metro + Fluent + Windows Themes - by mmak.es update {#metrology}
- **Entity ID**: `update.metrology_metro_fluent_windows_themes_by_mmak_es_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v.1.9.1
    - **latest_version**: v.1.9.1
    - **release_summary**: None
    - **release_url**: https://github.com/Madelena/Metrology-for-Hass/releases/v.1.9.1
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Midnight Theme update {#midnight}
- **Entity ID**: `update.midnight_theme_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: 07e1645
    - **latest_version**: 07e1645
    - **release_summary**: None
    - **release_url**: https://github.com/home-assistant-community-themes/midnight
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## mini-graph-card update {#mini}
- **Entity ID**: `update.mini_graph_card_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:47:42
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v0.13.0
    - **latest_version**: v0.13.0
    - **release_summary**: None
    - **release_url**: https://github.com/kalkih/mini-graph-card/releases/v0.13.0
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Mushroom update {#mushroom}
- **Entity ID**: `update.mushroom_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:43:47
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v4.4.0
    - **latest_version**: v4.4.0
    - **release_summary**: None
    - **release_url**: https://github.com/piitaya/lovelace-mushroom/releases/v4.4.0
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Pi {#pi}
*3 entities*

### Pi-Hole Core update available
- **Entity ID**: `update.pi_hole_core_update_available` | **State**: `off`
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/pi_hole/icon.png
    - **in_progress**: False
    - **installed_version**: v5.18.3
    - **latest_version**: v5.18.3
    - **release_url**: https://github.com/pi-hole/pi-hole/releases/tag/v5.18.3
    - **supported_features**: 0
    - **title**: Pi-hole Core

### Pi-Hole FTL update available
- **Entity ID**: `update.pi_hole_ftl_update_available` | **State**: `off`
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/pi_hole/icon.png
    - **in_progress**: False
    - **installed_version**: v5.25.2
    - **latest_version**: v5.25.2
    - **release_url**: https://github.com/pi-hole/FTL/releases/tag/v5.25.2
    - **supported_features**: 0
    - **title**: Pi-hole FTL DNS

### Pi-Hole Web update available
- **Entity ID**: `update.pi_hole_web_update_available` | **State**: `off`
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/pi_hole/icon.png
    - **in_progress**: False
    - **installed_version**: v5.21
    - **latest_version**: v5.21
    - **release_url**: https://github.com/pi-hole/AdminLTE/releases/tag/v5.21
    - **supported_features**: 0
    - **title**: Pi-hole Web interface

## Power Flow Card Plus update {#power}
- **Entity ID**: `update.power_flow_card_plus_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:45:12
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v0.2.5
    - **latest_version**: v0.2.5
    - **release_summary**: None
    - **release_url**: https://github.com/flixlix/power-flow-card-plus/releases/v0.2.5
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Reolink zahrada Firmware {#reolink}
- **Entity ID**: `update.reolink_zahrada_firmware`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/reolink/icon.png
    - **in_progress**: False
    - **installed_version**: v3.0.0.4348_2411261894
    - **latest_version**: v3.0.0.4348_2411261894
    - **release_summary**: None
    - **release_url**: https://reolink.com/download-center/
    - **skipped_version**: None
    - **supported_features**: 1
    - **title**: None
    - **update_percentage**: None

## Sankey Chart Card update {#sankey}
- **Entity ID**: `update.sankey_chart_card_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:46:14
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v3.8.1
    - **latest_version**: v3.8.1
    - **release_summary**: None
    - **release_url**: https://github.com/MindFreeze/ha-sankey-chart/releases/v3.8.1
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## sensor-uniku-vody-koupelna-nahore Firmware {#sensor}
- **Entity ID**: `update.sensor_uniku_vody_koupelna_nahore_firmware`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 2
    - **entity_picture**: https://brands.home-assistant.io/_/zha/icon.png
    - **in_progress**: False
    - **installed_version**: None
    - **latest_version**: None
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## shelly-klimatizace firmware {#shelly-klimatizace}
- **Entity ID**: `update.shelly_klimatizace_firmware`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:45
- **Last Updated**: 2025-06-08 19:41:45
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/shelly/icon.png
    - **in_progress**: False
    - **installed_version**: 1.6.2
    - **latest_version**: 1.6.2
    - **release_summary**: None
    - **release_url**: https://shelly-api-docs.shelly.cloud/gen2/changelog/
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None

## Shellymini Koupelna {#shellymini-koupelna}
*2 entities*

### shellymini-koupelna-nahore-01 firmware update
- **Entity ID**: `update.shellymini_koupelna_nahore_01_firmware_update` | **State**: `off`
- **Key Attributes**: device_class: firmware
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/shelly/icon.png
    - **in_progress**: False
    - **installed_version**: 1.6.2
    - **latest_version**: 1.6.2
    - **release_url**: https://shelly-api-docs.shelly.cloud/gen2/changelog/
    - **supported_features**: 5

### shellymini-koupelna-nahore-02 firmware update
- **Entity ID**: `update.shellymini_koupelna_nahore_02_firmware_update` | **State**: `off`
- **Key Attributes**: device_class: firmware
- **Other Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/shelly/icon.png
    - **in_progress**: False
    - **installed_version**: 1.6.2
    - **latest_version**: 1.6.2
    - **release_url**: https://shelly-api-docs.shelly.cloud/gen2/changelog/
    - **supported_features**: 5

## SmartThinQ LGE Sensors update {#smartthinq}
- **Entity ID**: `update.smartthinq_lge_sensors_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/smartthinq_sensors/icon.png
    - **in_progress**: False
    - **installed_version**: v0.41.1
    - **latest_version**: v0.41.1
    - **release_summary**: None
    - **release_url**: https://github.com/ollo69/ha-smartthinq-sensors/releases/v0.41.1
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Synology DSM update {#synology}
- **Entity ID**: `update.synology_dsm_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **attribution**: Data provided by Synology
    - **auto_update**: False
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/synology_dsm/icon.png
    - **in_progress**: False
    - **installed_version**: DSM 7.2.2-72806 Update 3
    - **latest_version**: DSM 7.2.2-72806 Update 3
    - **release_summary**: None
    - **release_url**: None
    - **skipped_version**: None
    - **supported_features**: 0
    - **title**: Synology DSM
    - **update_percentage**: None

## Synthwave Hass update {#synthwave}
- **Entity ID**: `update.synthwave_hass_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: 0.3.6
    - **latest_version**: 0.3.6
    - **release_summary**: None
    - **release_url**: https://github.com/bbbenji/synthwave-hass/releases/0.3.6
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Vacuum Card update {#vacuum}
- **Entity ID**: `update.vacuum_card_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:44:57
- **Attributes**:
    - **auto_update**: False
    - **display_precision**: 0
    - **in_progress**: False
    - **installed_version**: v2.11.0
    - **latest_version**: v2.11.0
    - **release_summary**: None
    - **release_url**: https://github.com/denysdovhan/vacuum-card/releases/v2.11.0
    - **skipped_version**: None
    - **supported_features**: 23
    - **title**: None
    - **update_percentage**: None

## Zasuvka Lidl Outdoor Firmware {#zasuvka}
- **Entity ID**: `update.zasuvka_lidl_outdoor_firmware`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **device_class**: firmware
    - **entity_picture**: https://brands.home-assistant.io/_/zha/icon.png
    - **supported_features**: 23

## zasuvky-technicka-mistnost firmware update {#zasuvky}
- **Entity ID**: `update.zasuvky_technicka_mistnost_firmware_update`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:45
- **Last Updated**: 2025-06-08 19:41:45
- **Attributes**:
    - **auto_update**: False
    - **device_class**: firmware
    - **display_precision**: 0
    - **entity_picture**: https://brands.home-assistant.io/_/shelly/icon.png
    - **in_progress**: False
    - **installed_version**: 1.6.2
    - **latest_version**: 1.6.2
    - **release_summary**: None
    - **release_url**: https://shelly-api-docs.shelly.cloud/gen2/changelog/
    - **skipped_version**: None
    - **supported_features**: 5
    - **title**: None
    - **update_percentage**: None
