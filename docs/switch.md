# Switch Entities

[← Back to Overview](./index.md)

*Total entities: 88*

## Table of Contents

- [0X000D6Ffffefc30F1](#0x000d6ffffefc30f1)
- [0X5C0272Fffe22643A](#0x5c0272fffe22643a)
- [0X7Cb03Eaa00B25De1](#0x7cb03eaa00b25de1)
- [0X84182600000Da73E](#0x84182600000da73e)
- [0X8418260000105301](#0x8418260000105301)
- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [0Xa4C13887Ab4A50C0](#0xa4c13887ab4a50c0)
- [Chladnicka](#chladnicka)
- [Klimatizace Pokoj](#klimatizace-pokoj)
- [Klimatizace Pracovna](#klimatizace-pracovna)
- [Ko<PERSON>](#kotel)
- [Pi](#pi)
- [Reolink](#reolink)
- [<PERSON><PERSON>](#shelly-klimatizace)
- [Sonoff 3](#sonoff-3)
- [Sonoff 4Ch01](#sonoff-4ch01)
- [Synology](#synology)
- [Ulice](#ulice)
- [Xiaomi Robot Vacuum X10](#xiaomi-robot-vacuum-x10)
- [Zahrada](#zahrada)
- [Zasuvka](#zasuvka)
- [Zasuvky Technicka Mistnost](#zasuvky-technicka-mistnost)
- [Zigbee2Mqtt](#zigbee2mqtt)

## 0x000d6ffffefc30f1 {#0x000d6ffffefc30f1}
- **Entity ID**: `switch.0x000d6ffffefc30f1`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50

## 0x5c0272fffe22643a do_not_disturb {#0x5c0272fffe22643a}
- **Entity ID**: `switch.0x5c0272fffe22643a_do_not_disturb`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Zasuvka CUBE (Osram) {#0x7cb03eaa00b25de1}
- **Entity ID**: `switch.0x7cb03eaa00b25de1`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50

## 0x84182600000da73e {#0x84182600000da73e}
- **Entity ID**: `switch.0x84182600000da73e`
- **State**: `off`
- **Last Changed**: 2025-06-09 07:18:05
- **Last Updated**: 2025-06-09 07:18:05

## Zasuvka Adelka pokoj {#0x8418260000105301}
- **Entity ID**: `switch.0x8418260000105301`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:50
- **Last Updated**: 2025-06-08 19:41:50

## 0X84Fd27Fffea36224 {#0x84fd27fffea36224}
*5 entities*

### 0x84fd27fffea36224 Child lock
- **Entity ID**: `switch.0x84fd27fffea36224_child_lock` | **State**: `off`

### 0x84fd27fffea36224 Frost protection
- **Entity ID**: `switch.0x84fd27fffea36224_frost_protection` | **State**: `off`

### 0x84fd27fffea36224 Heating stop
- **Entity ID**: `switch.0x84fd27fffea36224_heating_stop` | **State**: `off`

### 0x84fd27fffea36224 Online
- **Entity ID**: `switch.0x84fd27fffea36224_online` | **State**: `on`

### 0x84fd27fffea36224 Open window
- **Entity ID**: `switch.0x84fd27fffea36224_open_window` | **State**: `off`

## 0Xa4C13887Ab4A50C0 {#0xa4c13887ab4a50c0}
*2 entities*

### 0xa4c13887ab4a50c0
- **Entity ID**: `switch.0xa4c13887ab4a50c0` | **State**: `off`

### 0xa4c13887ab4a50c0 Child lock
- **Entity ID**: `switch.0xa4c13887ab4a50c0_child_lock` | **State**: `off`

## Chladnicka {#chladnicka}
*5 entities*

### Chladnička Eco friendly
- **Entity ID**: `switch.chladnicka_eco_friendly` | **State**: `off`
- **Key Attributes**: device_class: switch

### Chladnička Express cool
- **Entity ID**: `switch.chladnicka_express_cool` | **State**: `off`
- **Key Attributes**: device_class: switch

### Chladnička Express fridge
- **Entity ID**: `switch.chladnicka_express_fridge` | **State**: `off`
- **Key Attributes**: device_class: switch

### Chladnička Express mode
- **Entity ID**: `switch.chladnicka_express_mode` | **State**: `on`
- **Key Attributes**: device_class: switch

### Chladnička Express mode
- **Entity ID**: `switch.chladnicka_express_mode_2` | **State**: `on`
- **Key Attributes**: device_class: switch

## Klimatizace Pokoj {#klimatizace-pokoj}
*12 entities*

### Klimatizace pokoj levý Display light
- **Entity ID**: `switch.klimatizace_pokoj_levy_display_light` | **State**: `on`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj levý Energy saving
- **Entity ID**: `switch.klimatizace_pokoj_levy_energy_saving` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj levý Ionizer
- **Entity ID**: `switch.klimatizace_pokoj_levy_ionizer` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj levý Jet mode
- **Entity ID**: `switch.klimatizace_pokoj_levy_jet_mode` | **State**: `unavailable`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj levý Power
- **Entity ID**: `switch.klimatizace_pokoj_levy_power` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj pravý Energy saving
- **Entity ID**: `switch.klimatizace_pokoj_pravy_energy_saving` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj pravý Power
- **Entity ID**: `switch.klimatizace_pokoj_pravy_power` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj stred Display light
- **Entity ID**: `switch.klimatizace_pokoj_stred_display_light` | **State**: `on`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj stred Energy saving
- **Entity ID**: `switch.klimatizace_pokoj_stred_energy_saving` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj stred Ionizer
- **Entity ID**: `switch.klimatizace_pokoj_stred_ionizer` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj stred Jet mode
- **Entity ID**: `switch.klimatizace_pokoj_stred_jet_mode` | **State**: `unavailable`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj stred Power
- **Entity ID**: `switch.klimatizace_pokoj_stred_power` | **State**: `off`
- **Key Attributes**: device_class: switch

## Klimatizace Pracovna {#klimatizace-pracovna}
*3 entities*

### Klimatizace pokoj pravý Display light
- **Entity ID**: `switch.klimatizace_pracovna_display_light` | **State**: `on`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj pravý Ionizer
- **Entity ID**: `switch.klimatizace_pracovna_ionizer` | **State**: `off`
- **Key Attributes**: device_class: switch

### Klimatizace pokoj pravý Jet mode
- **Entity ID**: `switch.klimatizace_pracovna_jet_mode` | **State**: `unavailable`
- **Key Attributes**: device_class: switch

## Kotel {#kotel}
- **Entity ID**: `switch.kotel`
- **State**: `off`
- **Last Changed**: 2025-06-09 19:34:34
- **Last Updated**: 2025-06-09 19:34:34
- **Attributes**:
    - **icon**: mdi:radiator

## Pi-Hole {#pi}
- **Entity ID**: `switch.pi_hole`
- **State**: `on`
- **Last Changed**: 2025-06-10 07:06:45
- **Last Updated**: 2025-06-10 07:06:45
- **Attributes**:
    - **icon**: mdi:pi-hole

## Reolink {#reolink}
*7 entities*

### Reolink zahrada Email on event
- **Entity ID**: `switch.reolink_zahrada_email_on_event` | **State**: `off`

### Reolink zahrada FTP upload
- **Entity ID**: `switch.reolink_zahrada_ftp_upload` | **State**: `off`

### Reolink zahrada Infrared lights in night mode
- **Entity ID**: `switch.reolink_zahrada_infrared_lights_in_night_mode` | **State**: `on`

### Reolink zahrada Push notifications
- **Entity ID**: `switch.reolink_zahrada_push_notifications` | **State**: `on`

### Reolink zahrada Record
- **Entity ID**: `switch.reolink_zahrada_record` | **State**: `on`

### Reolink zahrada Record audio
- **Entity ID**: `switch.reolink_zahrada_record_audio` | **State**: `on`

### Reolink zahrada Siren on event
- **Entity ID**: `switch.reolink_zahrada_siren_on_event` | **State**: `off`

## shelly-klimatizace Switch 0 {#shelly-klimatizace}
- **Entity ID**: `switch.shelly_klimatizace_switch_0`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:41:45
- **Last Updated**: 2025-06-08 19:41:45

## switch.sonoff_3 {#sonoff-3}
- **Entity ID**: `switch.sonoff_3`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:53
- **Last Updated**: 2025-06-08 19:41:53
- **Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Sonoff 4Ch01 {#sonoff-4ch01}
*4 entities*

### Tasmota
- **Entity ID**: `switch.sonoff_4ch01_1` | **State**: `on`

### Tasmota Tasmota2
- **Entity ID**: `switch.sonoff_4ch01_1_sonoff_4ch01_2` | **State**: `off`

### Tasmota Tasmota3
- **Entity ID**: `switch.sonoff_4ch01_1_sonoff_4ch01_3` | **State**: `off`

### Tasmota Tasmota4
- **Entity ID**: `switch.sonoff_4ch01_1_sonoff_4ch01_4` | **State**: `on`

## Synology Surveillance Station Home mode {#synology}
- **Entity ID**: `switch.synology_surveillance_station_home_mode`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **attribution**: Data provided by Synology

## Ulice {#ulice}
*4 entities*

### Ulice Detect
- **Entity ID**: `switch.ulice_detect` | **State**: `on`

### Ulice Motion
- **Entity ID**: `switch.ulice_motion` | **State**: `on`

### Ulice Recordings
- **Entity ID**: `switch.ulice_recordings` | **State**: `on`

### Ulice Snapshots
- **Entity ID**: `switch.ulice_snapshots` | **State**: `on`

## Xiaomi Robot Vacuum X10 {#xiaomi-robot-vacuum-x10}
*22 entities*

### Xiaomi Robot Vacuum X10+ AI Fluid Detection
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_ai_fluid_detection` | **State**: `on`

### Xiaomi Robot Vacuum X10+ AI Furniture Detection
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_ai_furniture_detection` | **State**: `on`

### Xiaomi Robot Vacuum X10+ AI Image Upload
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_ai_image_upload` | **State**: `off`

### Xiaomi Robot Vacuum X10+ AI Obstacle Detection
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_ai_obstacle_detection` | **State**: `on`

### Xiaomi Robot Vacuum X10+ AI Obstacle Picture
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_ai_obstacle_picture` | **State**: `on`

### Xiaomi Robot Vacuum X10+ AI Pet Detection
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_ai_pet_detection` | **State**: `off`

### Xiaomi Robot Vacuum X10+ Auto-Add Detergent
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_auto_add_detergent` | **State**: `off`
- **Other Attributes**:
    - **value**: 0

### Xiaomi Robot Vacuum X10+ Auto Dust Collecting
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_auto_dust_collecting` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Auto Mount Mop
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_auto_mount_mop` | **State**: `off`

### Xiaomi Robot Vacuum X10+ Carpet Avoidance
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_carpet_avoidance` | **State**: `off`
- **Other Attributes**:
    - **value**: 2

### Xiaomi Robot Vacuum X10+ Carpet Boost
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_carpet_boost` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Carpet Recognition
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_carpet_recognition` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Child Lock
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_child_lock` | **State**: `off`

### Xiaomi Robot Vacuum X10+ Cleaning Sequence
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_cleaning_sequence` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Customized Cleaning
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_customized_cleaning` | **State**: `off`

### Xiaomi Robot Vacuum X10+ DnD
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_dnd` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Multi Floor Map
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_multi_floor_map` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Obstacle Avoidance
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_obstacle_avoidance` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Resume Cleaning
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_resume_cleaning` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Self-Clean
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_self_clean` | **State**: `on`

### Xiaomi Robot Vacuum X10+ Tight Mopping
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_tight_mopping` | **State**: `off`

### Xiaomi Robot Vacuum X10+ Water Electrolysis
- **Entity ID**: `switch.xiaomi_robot_vacuum_x10_water_electrolysis` | **State**: `on`

## Zahrada {#zahrada}
*8 entities*

### Zahrada-Bouda Detect
- **Entity ID**: `switch.zahrada_bouda_detect` | **State**: `on`

### Zahrada-Bouda Motion
- **Entity ID**: `switch.zahrada_bouda_motion` | **State**: `on`

### Zahrada-Bouda Recordings
- **Entity ID**: `switch.zahrada_bouda_recordings` | **State**: `on`

### Zahrada-Bouda Snapshots
- **Entity ID**: `switch.zahrada_bouda_snapshots` | **State**: `on`

### Zahrada Detect
- **Entity ID**: `switch.zahrada_detect` | **State**: `on`

### Zahrada Motion
- **Entity ID**: `switch.zahrada_motion` | **State**: `on`

### Zahrada Recordings
- **Entity ID**: `switch.zahrada_recordings` | **State**: `on`

### Zahrada Snapshots
- **Entity ID**: `switch.zahrada_snapshots` | **State**: `on`

## Zasuvka {#zasuvka}
*3 entities*

### LED
- **Entity ID**: `switch.zasuvka_led` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

### Zasuvka Lidl Outdoor Switch
- **Entity ID**: `switch.zasuvka_lidl_outdoor_switch` | **State**: `unavailable`

### Zásuvka TP Link OpenMediaVault
- **Entity ID**: `switch.zasuvka_tp_link` | **State**: `unavailable`
- **Other Attributes**:
    - **restored**: True
    - **supported_features**: 0

## Zasuvky Technicka Mistnost {#zasuvky-technicka-mistnost}
*2 entities*

### switch-0-pravy
- **Entity ID**: `switch.zasuvky_technicka_mistnost_switch_0` | **State**: `on`

### switch-1-levy
- **Entity ID**: `switch.zasuvky_technicka_mistnost_switch_1` | **State**: `on`

## Zigbee2MQTT Bridge Permit join {#zigbee2mqtt}
- **Entity ID**: `switch.zigbee2mqtt_bridge_permit_join`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:51
- **Last Updated**: 2025-06-08 19:41:51
- **Attributes**:
    - **icon**: mdi:human-greeting-proximity
