# Climate Entities

[← Back to Overview](./index.md)

*Total entities: 17*

## Table of Contents

- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [Chladnicka](#chladnicka)
- [Chodba](#chodba)
- [Klimatizac<PERSON> Pokoj](#klimatizace-pokoj)
- [Klimatizace Pracovna](#klimatizace-pracovna)
- [<PERSON><PERSON><PERSON><PERSON>](#koupelna)
- [Loznice](#loznice)
- [Oby<PERSON>ci](#obyvaci)
- [Pokoj](#pokoj)

## 0x84fd27fffea36224 {#0x84fd27fffea36224}
- **Entity ID**: `climate.0x84fd27fffea36224`
- **State**: `heat`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-10 05:55:30
- **Attributes**:
    - **current_temperature**: 22.1
    - **hvac_modes**: [
        "off",
        "heat"
      ]
    - **max_temp**: 30.0
    - **min_temp**: 5.0
    - **preset_mode**: auto
    - **preset_modes**: [
        "none",
        "auto",
        "manual",
        "holiday"
      ]
    - **supported_features**: 401
    - **target_temp_step**: 0.5
    - **temperature**: 21.0

## Chladnicka {#chladnicka}
*2 entities*

### Chladnička Freezer
- **Entity ID**: `climate.chladnicka_freezer` | **State**: `auto`
- **Other Attributes**:
    - **hvac_modes**: [
        "auto"
      ]
    - **max_temp**: -15
    - **min_temp**: -23
    - **supported_features**: 1
    - **target_temp_step**: 1
    - **temperature**: -21

### Chladnička Fridge
- **Entity ID**: `climate.chladnicka_fridge` | **State**: `auto`
- **Other Attributes**:
    - **hvac_modes**: [
        "auto"
      ]
    - **max_temp**: 7
    - **min_temp**: 1
    - **supported_features**: 1
    - **target_temp_step**: 1
    - **temperature**: 5

## Chodba {#chodba}
- **Entity ID**: `climate.chodba`
- **State**: `unknown`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-08 19:41:48
- **Attributes**:
    - **current_temperature**: None
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: None

## Klimatizace Pokoj {#klimatizace-pokoj}
*5 entities*

### Klimatizace pokoj levý
- **Entity ID**: `climate.klimatizace_pokoj_levy` | **State**: `off`
- **Other Attributes**:
    - **current_temperature**: 24.0
    - **fan_mode**: low
    - **fan_modes**: [
        "low",
        "LOW_MID",
        "medium",
        "MID_HIGH",
        "high",
        "diffuse"
      ]
    - **hvac_modes**: [
        "off",
        "heat",
        "dry",
        "cool",
        "fan_only",
        "heat_cool"
      ]
    - **max_temp**: 30.0
    - **min_temp**: 16.0
    - **supported_features**: 937
    - **swing_horizontal_mode**: Center
    - **swing_horizontal_modes**: [
        "Off",
        "Left",
        "MiddleLeft",
        "Center",
        "MiddleRight",
        "Right",
        "LeftHalf",
        "RightHalf",
        "Swing"
      ]
    - **swing_mode**: Top
    - **swing_modes**: [
        "Off",
        "Top",
        "MiddleTop1",
        "MiddleTop2",
        "MiddleBottom2",
        "MiddleBottom1",
        "Bottom",
        "Swing"
      ]
    - **target_temp_step**: 1.0
    - **temperature**: 24.0

### Klimatizace pokoj levý
- **Entity ID**: `climate.klimatizace_pokoj_levy_2` | **State**: `off`
- **Other Attributes**:
    - **current_temperature**: 24
    - **fan_mode**: low
    - **fan_modes**: [
        "high",
        "mid",
        "auto",
        "low"
      ]
    - **hvac_modes**: [
        "off",
        "fan_only",
        "heat",
        "dry",
        "cool",
        "auto"
      ]
    - **max_temp**: 30
    - **min_temp**: 18
    - **supported_features**: 395
    - **target_temp_step**: 0.5

### Klimatizace pokoj pravý
- **Entity ID**: `climate.klimatizace_pokoj_pravy` | **State**: `off`
- **Other Attributes**:
    - **current_temperature**: 24.5
    - **fan_mode**: low
    - **fan_modes**: [
        "mid",
        "low",
        "auto",
        "high"
      ]
    - **hvac_modes**: [
        "off",
        "dry",
        "cool",
        "fan_only",
        "heat",
        "auto"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 395

### Klimatizace pokoj stred
- **Entity ID**: `climate.klimatizace_pokoj_stred` | **State**: `off`
- **Other Attributes**:
    - **current_temperature**: 24.5
    - **fan_mode**: low
    - **fan_modes**: [
        "low",
        "LOW_MID",
        "medium",
        "MID_HIGH",
        "high",
        "diffuse"
      ]
    - **hvac_modes**: [
        "off",
        "heat",
        "dry",
        "cool",
        "fan_only",
        "heat_cool"
      ]
    - **max_temp**: 30.0
    - **min_temp**: 16.0
    - **supported_features**: 937
    - **swing_horizontal_mode**: Right
    - **swing_horizontal_modes**: [
        "Off",
        "Left",
        "MiddleLeft",
        "Center",
        "MiddleRight",
        "Right",
        "LeftHalf",
        "RightHalf",
        "Swing"
      ]
    - **swing_mode**: Off
    - **swing_modes**: [
        "Off",
        "Top",
        "MiddleTop1",
        "MiddleTop2",
        "MiddleBottom2",
        "MiddleBottom1",
        "Bottom",
        "Swing"
      ]
    - **target_temp_step**: 1.0
    - **temperature**: 23.0

### Klimatizace pokoj stred
- **Entity ID**: `climate.klimatizace_pokoj_stred_2` | **State**: `off`
- **Other Attributes**:
    - **current_temperature**: 24.5
    - **fan_mode**: low
    - **fan_modes**: [
        "low",
        "high",
        "mid",
        "auto"
      ]
    - **hvac_modes**: [
        "off",
        "dry",
        "fan_only",
        "heat",
        "cool",
        "auto"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 395

## Klimatizace pokoj pravý {#klimatizace-pracovna}
- **Entity ID**: `climate.klimatizace_pracovna`
- **State**: `off`
- **Last Changed**: 2025-06-08 19:41:48
- **Last Updated**: 2025-06-10 00:49:45
- **Attributes**:
    - **current_temperature**: 24.5
    - **fan_mode**: low
    - **fan_modes**: [
        "low",
        "LOW_MID",
        "medium",
        "MID_HIGH",
        "high",
        "diffuse"
      ]
    - **hvac_modes**: [
        "off",
        "heat",
        "dry",
        "cool",
        "fan_only",
        "heat_cool"
      ]
    - **max_temp**: 30.0
    - **min_temp**: 16.0
    - **supported_features**: 937
    - **swing_horizontal_mode**: LeftHalf
    - **swing_horizontal_modes**: [
        "Off",
        "Left",
        "MiddleLeft",
        "Center",
        "MiddleRight",
        "Right",
        "LeftHalf",
        "RightHalf",
        "Swing"
      ]
    - **swing_mode**: Bottom
    - **swing_modes**: [
        "Off",
        "Top",
        "MiddleTop1",
        "MiddleTop2",
        "MiddleBottom2",
        "MiddleBottom1",
        "Bottom",
        "Swing"
      ]
    - **target_temp_step**: 1.0
    - **temperature**: 24.0

## Koupelna {#koupelna}
*2 entities*

### Koupelna dole
- **Entity ID**: `climate.koupelna_dole` | **State**: `heat`
- **Other Attributes**:
    - **current_temperature**: 21.5
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 13.0

### Koupelna nahoře
- **Entity ID**: `climate.koupelna_nahore` | **State**: `heat`
- **Other Attributes**:
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 13.0

## Ložnice {#loznice}
- **Entity ID**: `climate.loznice`
- **State**: `heat`
- **Last Changed**: 2025-06-09 12:13:17
- **Last Updated**: 2025-06-10 06:52:13
- **Attributes**:
    - **current_temperature**: 23.0
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 20.5

## Obývací pokoj {#obyvaci}
- **Entity ID**: `climate.obyvaci_pokoj`
- **State**: `heat`
- **Last Changed**: 2025-06-09 12:13:17
- **Last Updated**: 2025-06-10 06:42:12
- **Attributes**:
    - **current_temperature**: 23.1
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 14.0

## Pokoj {#pokoj}
*3 entities*

### Pokoj dole
- **Entity ID**: `climate.pokoj_dole` | **State**: `heat`
- **Other Attributes**:
    - **current_temperature**: 22.1
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 14.5

### Pokoj levý
- **Entity ID**: `climate.pokoj_levy` | **State**: `heat`
- **Other Attributes**:
    - **current_temperature**: 22.9
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 20.5

### Pokoj pravý
- **Entity ID**: `climate.pokoj_pravy` | **State**: `heat`
- **Other Attributes**:
    - **current_temperature**: 23.5
    - **hvac_modes**: [
        "auto",
        "heat",
        "off"
      ]
    - **max_temp**: 35
    - **min_temp**: 7
    - **supported_features**: 385
    - **target_temp_step**: 0.5
    - **temperature**: 13.0
