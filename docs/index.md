# Home Assistant Instance Documentation

*Generated on: 2025-06-10 09:13:57*

## Configuration Overview

- **Version**: 2025.5.3
- **Location**: Home
- **Latitude**: 49.00313898
- **Longitude**: 14.54330206
- **Elevation**: 497 m
- **Time Zone**: Europe/Prague
- **Unit System**: Not set
- **Currency**: EUR
- **Country**: CZ
- **Language**: en

### Loaded Components (319)

**A**: alarm_control_panel, analytics, api, apple_tv, apple_tv.media_player, apple_tv.remote, application_credentials, assist_pipeline, auth, automation
**B**: backup, backup.sensor, binary_sensor, blueprint, bluetooth, bluetooth_adapters, button
**C**: camera, cast, cast.media_player, cert_expiry, cert_expiry.sensor, climate, cloud, cloud.tts, command_line, command_line.sensor, config, conversation, counter, cover
**D**: default_config, device_automation, device_tracker, dhcp, diagnostics, dlna_dmr, dlna_dmr.media_player, dlna_dms, dreame_vacuum, dreame_vacuum.button, dreame_vacuum.camera, dreame_vacuum.number, dreame_vacuum.select, dreame_vacuum.sensor, dreame_vacuum.switch, dreame_vacuum.vacuum
**E**: ecowitt, ecowitt.binary_sensor, ecowitt.sensor, energy, energy.sensor, esphome, esphome.sensor, event
**F**: fan, ffmpeg, file_upload, forecast_solar, forecast_solar.sensor, frigate, frigate.binary_sensor, frigate.camera, frigate.image, frigate.number, frigate.sensor, frigate.switch, frigate.update, frontend
**G**: generic, generic.camera, go2rtc, goodwe, goodwe.button, goodwe.number, goodwe.select, goodwe.sensor, google_assistant, google_assistant.button, google_translate.tts, group
**H**: hacs, hacs.switch, hacs.update, hardware, hikvision.binary_sensor, history, history_stats.sensor, homeassistant, homeassistant.scene, homeassistant_alerts, homeassistant_hardware, homekit, homekit_controller, homekit_controller.button, homekit_controller.light, http, humidifier
**I**: image, image_upload, input_boolean, input_button, input_datetime, input_number, input_select, input_text, integration.sensor, intent, ios, ios.notify, ios.sensor, ipp
**L**: lg_thinq, lg_thinq.binary_sensor, lg_thinq.climate, lg_thinq.event, lg_thinq.fan, lg_thinq.number, lg_thinq.select, lg_thinq.sensor, lg_thinq.switch, lg_thinq.vacuum, lg_thinq.water_heater, light, lock, logbook, logger, lovelace
**M**: media_player, media_source, met, met.weather, mobile_app, mobile_app.binary_sensor, mobile_app.device_tracker, mobile_app.notify, mobile_app.sensor, mqtt, mqtt.binary_sensor, mqtt.button, mqtt.climate, mqtt.light, mqtt.lock, mqtt.number, mqtt.select, mqtt.sensor, mqtt.switch, mqtt.text, mqtt.update, my
**N**: netatmo, netatmo.binary_sensor, netatmo.button, netatmo.camera, netatmo.climate, netatmo.cover, netatmo.fan, netatmo.light, netatmo.select, netatmo.sensor, netatmo.switch, network, nmap_tracker, nmap_tracker.device_tracker, notify, number, nut
**O**: onboarding, openweathermap, openweathermap.sensor, openweathermap.weather
**P**: persistent_notification, person, pi_hole, pi_hole.binary_sensor, pi_hole.sensor, pi_hole.switch, pi_hole.update, ping, ping.binary_sensor, ping.device_tracker, ping.sensor, prometheus, pushover, pushover.notify, python_script
**R**: recorder, remote, reolink, reolink.binary_sensor, reolink.button, reolink.camera, reolink.light, reolink.number, reolink.select, reolink.sensor, reolink.siren, reolink.switch, reolink.update, repairs, rest, rest.notify
**S**: scene, schedule, script, search, select, sensor, shelly, shelly.binary_sensor, shelly.button, shelly.climate, shelly.cover, shelly.event, shelly.light, shelly.number, shelly.select, shelly.sensor, shelly.switch, shelly.text, shelly.update, shelly.valve, siren, smartthinq_sensors, smartthinq_sensors.binary_sensor, smartthinq_sensors.button, smartthinq_sensors.climate, smartthinq_sensors.fan, smartthinq_sensors.humidifier, smartthinq_sensors.light, smartthinq_sensors.select, smartthinq_sensors.sensor, smartthinq_sensors.switch, smartthinq_sensors.water_heater, ssdp, stream, stt, sun, sun.sensor, switch, synology_dsm, synology_dsm.binary_sensor, synology_dsm.button, synology_dsm.camera, synology_dsm.sensor, synology_dsm.switch, synology_dsm.update, system_health, system_log, systemmonitor, systemmonitor.binary_sensor, systemmonitor.sensor
**T**: tag, tasmota, tasmota.binary_sensor, tasmota.cover, tasmota.fan, tasmota.light, tasmota.sensor, tasmota.switch, template, template.binary_sensor, template.sensor, text, thread, timer, tplink, trace, tts
**U**: update, usb, utility_meter, utility_meter.sensor
**V**: vacuum, valve, version, version.binary_sensor, version.sensor
**W**: wake_word, water_heater, waze_travel_time, waze_travel_time.sensor, weather, webhook, webostv, webostv.media_player, webostv.notify, websocket_api
**X**: xiaomi_aqara, xiaomi_aqara.binary_sensor, xiaomi_aqara.cover, xiaomi_aqara.light, xiaomi_aqara.lock, xiaomi_aqara.sensor, xiaomi_aqara.switch, xiaomi_miio, xiaomi_miio.binary_sensor, xiaomi_miio.button, xiaomi_miio.sensor, xiaomi_miio.vacuum
**Y**: yeelight
**Z**: zeroconf, zha, zha.alarm_control_panel, zha.binary_sensor, zha.button, zha.climate, zha.cover, zha.device_tracker, zha.fan, zha.light, zha.lock, zha.number, zha.select, zha.sensor, zha.siren, zha.switch, zha.update, zone


## Entity Summary by Domain

| Domain | Count | Documentation |
|--------|-------|---------------|
| Automation | 70 | [View Details](./automation.md) |
| Binary_Sensor | 91 | [View Details](./binary_sensor.md) |
| Button | 30 | [View Details](./button.md) |
| Camera | 11 | [View Details](./camera.md) |
| Climate | 17 | [View Details](./climate.md) |
| Conversation | 1 | [View Details](./conversation.md) |
| Device_Tracker | 22 | [View Details](./device_tracker.md) |
| Event | 4 | [View Details](./event.md) |
| Group | 1 | [View Details](./group.md) |
| Image | 3 | [View Details](./image.md) |
| Input_Boolean | 11 | [View Details](./input_boolean.md) |
| Input_Datetime | 8 | [View Details](./input_datetime.md) |
| Input_Number | 14 | [View Details](./input_number.md) |
| Input_Select | 2 | [View Details](./input_select.md) |
| Input_Text | 1 | [View Details](./input_text.md) |
| Light | 6 | [View Details](./light.md) |
| Lock | 2 | [View Details](./lock.md) |
| Media_Player | 10 | [View Details](./media_player.md) |
| Number | 28 | [View Details](./number.md) |
| Person | 4 | [View Details](./person.md) |
| Remote | 1 | [View Details](./remote.md) |
| Script | 4 | [View Details](./script.md) |
| Select | 53 | [View Details](./select.md) |
| Sensor | 646 | [View Details](./sensor.md) |
| Siren | 1 | [View Details](./siren.md) |
| Sun | 1 | [View Details](./sun.md) |
| Switch | 88 | [View Details](./switch.md) |
| Tag | 1 | [View Details](./tag.md) |
| Text | 1 | [View Details](./text.md) |
| Timer | 3 | [View Details](./timer.md) |
| Update | 38 | [View Details](./update.md) |
| Vacuum | 2 | [View Details](./vacuum.md) |
| Weather | 2 | [View Details](./weather.md) |
| Zone | 6 | [View Details](./zone.md) |
| **Total** | **1183** | - |

## Quick Statistics

### Top 10 Devices by Entity Count

| Device | Entity Count |
|--------|--------------|
| Xiaomi Robot Vacuum X10 | 99 |
| Pavel | 74 |
| Klimatizace Pokoj | 56 |
| Synology | 33 |
| Battery | 32 |
| Zahrada | 29 |
| Openweathermap | 28 |
| 0X84Fd27Fffea36224 | 26 |
| Pavels | 24 |
| Reolink | 24 |
