# Select Entities

[← Back to Overview](./index.md)

*Total entities: 53*

## Table of Contents

- [0X000D6Ffffefc30F1](#0x000d6ffffefc30f1)
- [0X00158D0002B8C8E7](#0x00158d0002b8c8e7)
- [0X001788010C6Cdbaf](#0x001788010c6cdbaf)
- [0X5C0272Fffe22643A](#0x5c0272fffe22643a)
- [0X84Fd27Fffea36224](#0x84fd27fffea36224)
- [0Xa4C13887Ab4A50C0](#0xa4c13887ab4a50c0)
- [Goodwe Inverter](#goodwe-inverter)
- [Reolink](#reolink)
- [Xiaomi Robot Vacuum X10](#xiaomi-robot-vacuum-x10)
- [Zigbee2Mqtt](#zigbee2mqtt)

## 0x000d6ffffefc30f1 Power-on behavior {#0x000d6ffffefc30f1}
- **Entity ID**: `select.0x000d6ffffefc30f1_power_on_behavior`
- **State**: `unavailable`
- **Last Changed**: 2025-06-08 19:41:49
- **Last Updated**: 2025-06-08 19:41:49
- **Attributes**:
    - **icon**: mdi:power-settings
    - **options**: [
        "off",
        "on",
        "toggle",
        "previous"
      ]

## 0X00158D0002B8C8E7 {#0x00158d0002b8c8e7}
*2 entities*

### 0x00158d0002b8c8e7 sensitivity
- **Entity ID**: `select.0x00158d0002b8c8e7_sensitivity` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "low",
        "medium",
        "high"
      ]
    - **restored**: True
    - **supported_features**: 0

### Sensitivity
- **Entity ID**: `select.0x00158d0002b8c8e7_sensitivity_2` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "low",
        "medium",
        "high"
      ]
    - **restored**: True
    - **supported_features**: 0

## 0x001788010c6cdbaf Power-on behavior {#0x001788010c6cdbaf}
- **Entity ID**: `select.0x001788010c6cdbaf_power_on_behavior`
- **State**: `on`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **icon**: mdi:power-settings
    - **options**: [
        "off",
        "on",
        "toggle",
        "previous"
      ]

## 0X5C0272Fffe22643A {#0x5c0272fffe22643a}
*2 entities*

### 0x5c0272fffe22643a color power on behavior
- **Entity ID**: `select.0x5c0272fffe22643a_color_power_on_behavior` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "initial",
        "previous",
        "cutomized"
      ]
    - **restored**: True
    - **supported_features**: 0

### 0x5c0272fffe22643a power on behavior
- **Entity ID**: `select.0x5c0272fffe22643a_power_on_behavior` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "off",
        "on",
        "toggle",
        "previous"
      ]
    - **restored**: True
    - **supported_features**: 0

## 0x84fd27fffea36224 Working day {#0x84fd27fffea36224}
- **Entity ID**: `select.0x84fd27fffea36224_working_day`
- **State**: `mon_sun`
- **Last Changed**: 2025-06-08 19:42:29
- **Last Updated**: 2025-06-08 19:42:29
- **Attributes**:
    - **options**: [
        "mon_sun",
        "mon_fri+sat+sun",
        "separate"
      ]

## 0Xa4C13887Ab4A50C0 {#0xa4c13887ab4a50c0}
*2 entities*

### 0xa4c13887ab4a50c0 Indicator mode
- **Entity ID**: `select.0xa4c13887ab4a50c0_indicator_mode` | **State**: `off/on`
- **Other Attributes**:
    - **options**: [
        "off",
        "off/on",
        "on/off",
        "on"
      ]

### 0xa4c13887ab4a50c0 Power outage memory
- **Entity ID**: `select.0xa4c13887ab4a50c0_power_outage_memory` | **State**: `restore`
- **Other Attributes**:
    - **options**: [
        "on",
        "off",
        "restore"
      ]

## GoodWe Inverter operation mode {#goodwe-inverter}
- **Entity ID**: `select.goodwe_inverter_operation_mode`
- **State**: `general`
- **Last Changed**: 2025-06-08 19:41:49
- **Last Updated**: 2025-06-08 19:41:49
- **Attributes**:
    - **options**: [
        "general",
        "off_grid",
        "backup",
        "eco",
        "peak_shaving"
      ]

## Reolink {#reolink}
*2 entities*

### Reolink zahrada Day night mode
- **Entity ID**: `select.reolink_zahrada_day_night_mode` | **State**: `auto`
- **Other Attributes**:
    - **options**: [
        "auto",
        "color",
        "blackwhite"
      ]

### Reolink zahrada Floodlight mode
- **Entity ID**: `select.reolink_zahrada_floodlight_mode` | **State**: `off`
- **Other Attributes**:
    - **options**: [
        "off",
        "auto",
        "schedule"
      ]

## Xiaomi Robot Vacuum X10 {#xiaomi-robot-vacuum-x10}
*40 entities*

### Xiaomi Robot Vacuum X10+ Auto Empty Frequency
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_auto_empty_frequency` | **State**: `1x`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]
    - **value**: 1

### Xiaomi Robot Vacuum X10+ Carpet Sensitivity
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_carpet_sensitivity` | **State**: `medium`
- **Key Attributes**: device_class: dreame_vacuum__carpet_sensitivity
- **Other Attributes**:
    - **options**: [
        "low",
        "medium",
        "high"
      ]
    - **value**: 2

### Xiaomi Robot Vacuum X10+ Cleaning Mode
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_cleaning_mode` | **State**: `sweeping`
- **Key Attributes**: device_class: dreame_vacuum__cleaning_mode
- **Other Attributes**:
    - **options**: [
        "sweeping",
        "mopping",
        "sweeping_and_mopping"
      ]
    - **value**: 201730

### Xiaomi Robot Vacuum X10+ Drying Time
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_drying_time` | **State**: `2h`
- **Other Attributes**:
    - **options**: [
        "2h",
        "3h",
        "4h"
      ]
    - **value**: 2

### Xiaomi Robot Vacuum X10+ Map Rotation
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_map_rotation` | **State**: `0`
- **Other Attributes**:
    - **options**: [
        "0",
        "90",
        "180",
        "270"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Mop Wash Level
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_mop_wash_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_wash_level
- **Other Attributes**:
    - **options**: [
        "deep",
        "daily",
        "water_saving"
      ]

### Xiaomi Robot Vacuum X10+ Cleaning Times Room 1
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_1_cleaning_times` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity Room 1
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_1_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Room 1 Name
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_1_name` | **State**: `Room 1`
- **Other Attributes**:
    - **index**: 0
    - **options**: [
        "Room 1",
        "Living Room",
        "Primary Bedroom",
        "Study",
        "Kitchen",
        "Dining Hall",
        "Bathroom",
        "Balcony",
        "Corridor 2",
        "Utility Room",
        "Closet",
        "Meeting Room",
        "Office",
        "Fitness Area",
        "Recreation Area",
        "Secondary Bedroom"
      ]
    - **room_id**: 1
    - **type**: 0

### Xiaomi Robot Vacuum X10+ Order Room 1
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_1_order` | **State**: `1`
- **Other Attributes**:
    - **options**: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level Room 1
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_1_suction_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]

### Xiaomi Robot Vacuum X10+ Cleaning Times Room 2
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_2_cleaning_times` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity Room 2
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_2_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Room 2 Name
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_2_name` | **State**: `Room 2`
- **Other Attributes**:
    - **index**: 0
    - **options**: [
        "Room 2",
        "Living Room",
        "Primary Bedroom",
        "Study",
        "Kitchen",
        "Dining Hall",
        "Bathroom",
        "Balcony",
        "Corridor 2",
        "Utility Room",
        "Closet",
        "Meeting Room",
        "Office",
        "Fitness Area",
        "Recreation Area",
        "Secondary Bedroom"
      ]
    - **room_id**: 2
    - **type**: 0

### Xiaomi Robot Vacuum X10+ Order Room 2
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_2_order` | **State**: `2`
- **Other Attributes**:
    - **options**: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level Room 2
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_2_suction_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]

### Xiaomi Robot Vacuum X10+ Cleaning Times Corridor
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_3_cleaning_times` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity Corridor
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_3_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Room 3 Name
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_3_name` | **State**: `Corridor`
- **Other Attributes**:
    - **index**: 0
    - **options**: [
        "Room 3",
        "Living Room",
        "Primary Bedroom",
        "Study",
        "Kitchen",
        "Dining Hall",
        "Bathroom",
        "Balcony",
        "Corridor",
        "Utility Room",
        "Closet",
        "Meeting Room",
        "Office",
        "Fitness Area",
        "Recreation Area",
        "Secondary Bedroom"
      ]
    - **room_id**: 3
    - **type**: 8

### Xiaomi Robot Vacuum X10+ Order Corridor
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_3_order` | **State**: `3`
- **Other Attributes**:
    - **options**: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level Corridor
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_3_suction_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]

### Xiaomi Robot Vacuum X10+ Cleaning Times Living Room
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_4_cleaning_times` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity Living Room
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_4_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Room 4 Name
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_4_name` | **State**: `Living room`
- **Other Attributes**:
    - **index**: 0
    - **options**: [
        "Living room",
        "Living Room",
        "Primary Bedroom",
        "Study",
        "Kitchen",
        "Dining Hall",
        "Bathroom",
        "Balcony",
        "Corridor 2",
        "Utility Room",
        "Closet",
        "Meeting Room",
        "Office",
        "Fitness Area",
        "Recreation Area",
        "Secondary Bedroom"
      ]
    - **room_id**: 4
    - **type**: 0

### Xiaomi Robot Vacuum X10+ Order Living Room
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_4_order` | **State**: `4`
- **Other Attributes**:
    - **options**: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level Living Room
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_4_suction_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]

### Xiaomi Robot Vacuum X10+ Cleaning Times Room 5
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_5_cleaning_times` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity Room 5
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_5_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Room 5 Name
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_5_name` | **State**: `Room 5`
- **Other Attributes**:
    - **index**: 0
    - **options**: [
        "Room 5",
        "Living Room",
        "Primary Bedroom",
        "Study",
        "Kitchen",
        "Dining Hall",
        "Bathroom",
        "Balcony",
        "Corridor 2",
        "Utility Room",
        "Closet",
        "Meeting Room",
        "Office",
        "Fitness Area",
        "Recreation Area",
        "Secondary Bedroom"
      ]
    - **room_id**: 5
    - **type**: 0

### Xiaomi Robot Vacuum X10+ Order Room 5
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_5_order` | **State**: `5`
- **Other Attributes**:
    - **options**: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level Room 5
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_5_suction_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]

### Xiaomi Robot Vacuum X10+ Cleaning Times Room 6
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_6_cleaning_times` | **State**: `unavailable`
- **Other Attributes**:
    - **options**: [
        "1x",
        "2x",
        "3x"
      ]

### Xiaomi Robot Vacuum X10+ Mop Pad Humidity Room 6
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_6_mop_pad_humidity` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__mop_pad_humidity
- **Other Attributes**:
    - **options**: [
        "slightly_dry",
        "moist",
        "wet"
      ]

### Xiaomi Robot Vacuum X10+ Room 6 Name
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_6_name` | **State**: `Room 6`
- **Other Attributes**:
    - **index**: 0
    - **options**: [
        "Room 6",
        "Living Room",
        "Primary Bedroom",
        "Study",
        "Kitchen",
        "Dining Hall",
        "Bathroom",
        "Balcony",
        "Corridor 2",
        "Utility Room",
        "Closet",
        "Meeting Room",
        "Office",
        "Fitness Area",
        "Recreation Area",
        "Secondary Bedroom"
      ]
    - **room_id**: 6
    - **type**: 0

### Xiaomi Robot Vacuum X10+ Order Room 6
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_6_order` | **State**: `6`
- **Other Attributes**:
    - **options**: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level Room 6
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_room_6_suction_level` | **State**: `unavailable`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]

### Xiaomi Robot Vacuum X10+ Selected Map
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_selected_map` | **State**: `Map 1`
- **Other Attributes**:
    - **map_id**: 1
    - **map_index**: 1
    - **options**: [
        "Map 1"
      ]

### Xiaomi Robot Vacuum X10+ Self Clean Area
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_self_clean_area` | **State**: `unknown`
- **Key Attributes**: device_class: dreame_vacuum__self_clean_area
- **Other Attributes**:
    - **options**: [
        "five_square_meters",
        "ten_square_meters",
        "fifteen_square_meters",
        "single_zone"
      ]

### Xiaomi Robot Vacuum X10+ Suction Level
- **Entity ID**: `select.xiaomi_robot_vacuum_x10_suction_level` | **State**: `standard`
- **Key Attributes**: device_class: dreame_vacuum__suction_level
- **Other Attributes**:
    - **options**: [
        "quiet",
        "standard",
        "strong",
        "turbo"
      ]
    - **value**: 1

## Zigbee2MQTT Bridge Log level {#zigbee2mqtt}
- **Entity ID**: `select.zigbee2mqtt_bridge_log_level`
- **State**: `info`
- **Last Changed**: 2025-06-08 19:41:51
- **Last Updated**: 2025-06-08 19:41:51
- **Attributes**:
    - **options**: [
        "error",
        "warning",
        "info",
        "debug"
      ]
