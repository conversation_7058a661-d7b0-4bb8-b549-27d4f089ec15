# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Home Assistant dashboard configuration project that defines a comprehensive smart home UI for a Czech household. The project includes:
- Main dashboard configuration in `dashboard.yaml` file (2,571 lines)
- Documentation generator script for Home Assistant entities
- Generated documentation in the `docs/` folder

## Architecture

### Dashboard Structure
The dashboard consists of 7 views (tabs):
- **Přehled** - Main overview dashboard with sections layout
- **Bezpečnost & Alarm** - Security and alarm system controls
- **Klima & Topení** - Climate control and heating management
- **surveillance** - Security camera feeds
- **Home** - Solar power monitoring and energy flow visualization
- **Historie & Analytika** - Historical data and analytics graphs
- **Topení** - Central heating system controls

### Custom Cards Used
The dashboard heavily relies on Mushroom cards (110+ instances) and other custom Home Assistant cards:
- `custom:mushroom-template-card` - Template-based UI cards (54 instances)
- `custom:mushroom-entity-card` - Entity display cards (42 instances)
- `custom:mushroom-climate-card` - Climate control cards (11 instances)
- `custom:mini-graph-card` - Compact graphs for sensor data (11 instances)
- `custom:power-flow-card-plus` - Energy flow visualization
- `custom:sankey-chart` - Energy distribution visualization
- `custom:advanced-camera-card` - Enhanced camera controls
- `custom:horizon-card` - Sunrise/sunset display
- Additional Mushroom variants for badges, numbers, and media players

## Development Workflow

### Making Changes
1. Edit `dashboard.yaml` directly
2. Save changes and reload the dashboard in Home Assistant UI
3. Check for YAML syntax errors in Home Assistant logs

### Testing Changes
- Changes are tested live in the Home Assistant environment
- Use Home Assistant's configuration validation: Settings → System → Check Configuration
- Monitor browser console for JavaScript errors when testing UI changes

### Common Commands

To validate YAML syntax locally:
```bash
# Install yamllint if needed
pip install yamllint

# Check syntax
yamllint dashboard.yaml
```

To reload dashboard without restarting Home Assistant:
- Navigate to Developer Tools → YAML → Lovelace Dashboards → Reload

## Entity Naming Convention

Entities follow Czech naming with descriptive patterns:
- **Climate entities**: `climate.klimatizace_[location]`, `climate.[room_name]`
- **Sensors**: `sensor.[device]_[measurement]`, `sensor.[metric]_[timeframe]`
- **Switches**: `switch.[device]_[function]`
- **Binary sensors**: `binary_sensor.[device]_[state]`, `binary_sensor.[location]_[type]`
- **Input booleans**: `input_boolean.[function]_[detail]`
- **Vacuum**: `vacuum.[brand]_[model]`

## Important Considerations

- All text displayed to users should be in Czech
- Maintain consistent card layouts within sections
- Group related controls logically
- Consider mobile responsiveness when adding new cards
- Monitor performance impact of complex cards (especially graphs and energy flows)
- The dashboard.yaml is a large file (2,571 lines) - make edits carefully to avoid breaking the entire dashboard
- Mushroom cards are the primary UI framework - maintain consistency with their design patterns

## Documentation Generation

### Home Assistant Documentation Generator

The project includes a Python script (`ha_documentation_generator.py`) that connects to Home Assistant via REST API to generate comprehensive documentation of all entities, devices, and their states.

#### Setup
```bash
# Create virtual environment (if not exists)
python -m venv .venv

# Install dependencies
.venv/bin/pip install requests python-dotenv

# Create .env file from example
cp .env.example .env

# Edit .env and add your Home Assistant Long-Lived Access Token
# HA_TOKEN=your_token_here
# HA_URL=https://hass.k8s.sklenarovi.cz
```

#### Usage
```bash
# Generate documentation using environment variables
.venv/bin/python ha_documentation_generator.py

# Or with command-line arguments
.venv/bin/python ha_documentation_generator.py --token YOUR_TOKEN --url YOUR_URL --output-dir custom-docs
```

#### Features
- Groups entities by device for better organization
- Creates separate markdown files for each domain (sensor, switch, light, etc.)
- Generates an overview page (index.md) with links to all domains
- Cleans output directory before each run to avoid conflicts
- Supports environment variables via .env file

#### Output Structure
```
docs/
├── index.md          # Overview page with configuration and statistics
├── sensor.md         # All sensor entities grouped by device
├── switch.md         # All switch entities grouped by device
├── light.md          # All light entities grouped by device
├── climate.md        # All climate entities grouped by device
├── binary_sensor.md  # All binary sensor entities
├── ...               # One file per entity domain
```

Each domain file includes:
- Total entity count
- Table of contents (for domains with many devices)
- Entities grouped by device with compact formatting
- Detailed attributes for single entities
- Navigation links back to overview