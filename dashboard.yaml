views:
  - title: <PERSON><PERSON><PERSON><PERSON>
    icon: mdi:view-dashboard
    type: sections
    max_columns: 4
    path: overview
    subview: false
    sections:
      - type: grid
        cards:
          - type: heading
            heading: FVE & Energie
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.pv_power'') }}W'
                secondary: Solární výroba
                icon: mdi:solar-panel
                icon_color: >
                  {% set power = states('sensor.pv_power') | float %}
                  {% if power > 3000 %}green{% elif power > 1000 %}orange{% else %}grey{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.pv_power
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.battery_state_of_charge'') }}%'
                secondary: Stav baterie
                icon: mdi:battery
                icon_color: >
                  {% set battery = states('sensor.battery_state_of_charge') | float %}
                  {% if battery > 70 %}green{% elif battery > 40 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.battery_state_of_charge
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.house_consumption'') }}W'
                secondary: Spotřeba domu
                icon: mdi:home-lightning-bolt
                icon_color: >
                  {% set consumption = states('sensor.house_consumption') | float %}
                  {% if consumption < 1000 %}green{% elif consumption < 3000 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.house_consumption
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >
                  {% set grid_buy = states('sensor.energy_buy') | float %}
                  {% set grid_sell = states('sensor.energy_sell') | float %}
                  {% if grid_buy > grid_sell %}{{ (grid_buy - grid_sell) | round(0) }}W odběr{%
                  elif grid_sell > grid_buy %}{{ (grid_sell - grid_buy) | round(0) }}W přetok{%
                  else %}0W{% endif %}
                secondary: Stav sítě
                icon: mdi:transmission-tower
                icon_color: >
                  {% set grid_buy = states('sensor.energy_buy') | float %}
                  {% set grid_sell = states('sensor.energy_sell') | float %}
                  {% if grid_buy > grid_sell %}red{% elif grid_sell > grid_buy %}green{% else %}grey{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.energy_buy
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.battery_power'') }}W'
                secondary: >
                  {% set power = states('sensor.battery_power') | float %}
                  {% if power > 0 %}Nabíjení{% elif power < 0 %}Vybíjení{% else %}Nečinná{% endif %}
                icon: mdi:battery-arrow-up
                icon_color: >
                  {% set power = states('sensor.battery_power') | float %}
                  {% if power > 0 %}green{% elif power < 0 %}orange{% else %}grey{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.battery_power
      - type: grid
        cards:
          - type: heading
            heading: Klima & Teploty
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >-
                  {{
                  states('sensor.netatmo_moje_domacnost_weather_station_temperature')
                  }}°C
                secondary: Vnitřní teplota
                icon: mdi:thermometer
                icon_color: >
                  {% set temp =
                  states('sensor.netatmo_moje_domacnost_weather_station_temperature')
                  | float %} {% if temp < 20 %}blue{% elif temp > 25 %}red{%
                  else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.netatmo_moje_domacnost_weather_station_temperature
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.gw1100a_temperature_1'') }}°C'
                secondary: Venkovní teplota
                icon: mdi:thermometer-lines
                icon_color: >
                  {% set temp = states('sensor.gw1100a_temperature_1') | float
                  %} {% if temp < 10 %}blue{% elif temp > 30 %}red{% else
                  %}orange{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.gw1100a_temperature_1
              - type: custom:mushroom-template-card
                primary: |
                  {% set ac_count = [
                    states('climate.klimatizace_pokoj_levy'),
                    states('climate.klimatizace_pokoj_stred'),
                    states('climate.klimatizace_pracovna')
                  ] | select('ne', 'off') | list | count %} {{ ac_count }}/3
                secondary: AC jednotky aktivní
                icon: mdi:air-conditioner
                icon_color: >
                  {% set ac_count = [
                    states('climate.klimatizace_pokoj_levy'),
                    states('climate.klimatizace_pokoj_stred'),
                    states('climate.klimatizace_pracovna')
                  ] | select('ne', 'off') | list | count %} {% if ac_count == 0
                  %}grey{% elif ac_count <= 1 %}green{% elif ac_count <= 2
                  %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: climate.klimatizace_pokoj_levy
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: switch.kotel
                name: Hlavní kotel
                icon: mdi:water-boiler
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: input_boolean.topna_sezona
                name: Topná sezóna
                icon: mdi:snowflake
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.shelly_klimatizace_switch_0_power'') }}W'
                secondary: Spotřeba AC
                icon: mdi:flash
                icon_color: >
                  {% set power = states('sensor.shelly_klimatizace_switch_0_power') | float %}
                  {% if power < 100 %}grey{% elif power < 1000 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.shelly_klimatizace_switch_0_power
      - type: grid
        cards:
          - type: heading
            heading: Rychlý přehled
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: |
                  {% set devices = [
                    states('binary_sensor.pracka_pere_status'),
                    states('binary_sensor.susicka_susi_status'),
                    states('binary_sensor.chladnicka_door')
                  ] | select('eq', 'on') | list | count %} {{ devices }} aktivní
                secondary: Spotřebiče
                icon: mdi:home-lightning-bolt
                icon_color: >
                  {% set devices = [
                    states('binary_sensor.pracka_pere_status'),
                    states('binary_sensor.susicka_susi_status'),
                    states('binary_sensor.chladnicka_door')
                  ] | select('eq', 'on') | list | count %} {% if devices == 0
                  %}green{% elif devices <= 2 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: binary_sensor.pracka_pere_status
              - type: custom:mushroom-template-card
                primary: >
                  {% set vacuums = [
                    states('vacuum.xiaomi_robot_vacuum_x10'),
                    states('vacuum.vysavac_horni_patro')
                  ] | select('eq', 'cleaning') | list | count %} {% if vacuums >
                  0 %}{{ vacuums }} aktivní{% else %}Nečinné{% endif %}
                secondary: Vysavače
                icon: mdi:robot-vacuum
                icon_color: >
                  {% set vacuums = [
                    states('vacuum.xiaomi_robot_vacuum_x10'),
                    states('vacuum.vysavac_horni_patro')
                  ] | select('eq', 'cleaning') | list | count %} {% if vacuums >
                  0 %}blue{% else %}grey{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: vacuum.xiaomi_robot_vacuum_x10
              - type: custom:mushroom-template-card
                primary: >
                  {% set work_time =
                  states('sensor.cesta_do_cloudfield_z_domova') |
                  regex_replace(' min', '') | int %} {% set parking_time =
                  states('sensor.cesta_na_parkoviste') | regex_replace(' min',
                  '') | int %} {% if work_time < 25 and parking_time < 15
                  %}Dobrá{% elif work_time < 35 and parking_time < 25
                  %}Střední{% else %}Špatná{% endif %}
                secondary: Dopravní situace
                icon: mdi:car
                icon_color: >
                  {% set work_time =
                  states('sensor.cesta_do_cloudfield_z_domova') |
                  regex_replace(' min', '') | int %} {% set parking_time =
                  states('sensor.cesta_na_parkoviste') | regex_replace(' min',
                  '') | int %} {% if work_time < 25 and parking_time < 15
                  %}green{% elif work_time < 35 and parking_time < 25 %}orange{%
                  else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.cesta_do_cloudfield_z_domova
      - type: grid
        cards:
          - type: heading
            heading: Kvalita vzduchu (CO₂)
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.netatmo_moje_domacnost_weather_station_co2'') }} ppm'
                secondary: CO₂ Obývák
                icon: mdi:molecule-co2
                icon_color: >
                  {% set co2 = states('sensor.netatmo_moje_domacnost_weather_station_co2') | float %}
                  {% if co2 < 1000 %}green{% elif co2 < 2000 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.netatmo_moje_domacnost_weather_station_co2
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2'') }} ppm'
                secondary: CO₂ Ložnice
                icon: mdi:molecule-co2
                icon_color: >
                  {% set co2 = states('sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2') | float %}
                  {% if co2 < 1000 %}green{% elif co2 < 2000 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2'') }} ppm'
                secondary: CO₂ Pracovna
                icon: mdi:molecule-co2
                icon_color: >
                  {% set co2 = states('sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2') | float %}
                  {% if co2 < 1000 %}green{% elif co2 < 2000 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2

      - type: grid
        cards:
          - type: heading
            heading: Teploty v místnostech
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.teplota_obyvaci_pokoj'') }}°C'
                secondary: Obývák
                icon: mdi:sofa
                icon_color: >
                  {% set temp = states('sensor.teplota_obyvaci_pokoj') | float %}
                  {% if temp < 20 %}blue{% elif temp > 25 %}red{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.teplota_obyvaci_pokoj
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.teplota_loznice'') }}°C'
                secondary: Ložnice
                icon: mdi:bed
                icon_color: >
                  {% set temp = states('sensor.teplota_loznice') | float %}
                  {% if temp < 20 %}blue{% elif temp > 25 %}red{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.teplota_loznice
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.teplota_pokoj_levy'') }}°C'
                secondary: Pokoj levý (Adélka)
                icon: mdi:account-child
                icon_color: >
                  {% set temp = states('sensor.teplota_pokoj_levy') | float %}
                  {% if temp < 20 %}blue{% elif temp > 25 %}red{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.teplota_pokoj_levy
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.teplota_pokoj_pravy'') }}°C'
                secondary: Pracovna
                icon: mdi:desk
                icon_color: >
                  {% set temp = states('sensor.teplota_pokoj_pravy') | float %}
                  {% if temp < 20 %}blue{% elif temp > 25 %}red{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.teplota_pokoj_pravy
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.teplota_pokoj_dole'') }}°C'
                secondary: Pokoj dole
                icon: mdi:home-floor-1
                icon_color: >
                  {% set temp = states('sensor.teplota_pokoj_dole') | float %}
                  {% if temp < 20 %}blue{% elif temp > 25 %}red{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.teplota_pokoj_dole
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.teplota_koupelna_nahore'') }}°C'
                secondary: Koupelna nahoře
                icon: mdi:shower
                icon_color: >
                  {% set temp = states('sensor.teplota_koupelna_nahore') | float %}
                  {% if temp < 20 %}blue{% elif temp > 25 %}red{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.teplota_koupelna_nahore

  - title: Bezpečnost & Alarm
    icon: mdi:shield-home
    type: sections
    max_columns: 4
    path: security
    subview: false
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Přehled zabezpečení
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >
                  {% set contacts = [
                    states('binary_sensor.0x00158d000315c387_contact'),
                    states('binary_sensor.0x00158d0002e214d4_contact'),
                    states('binary_sensor.0x00158d000315fac9_contact')
                  ] | select('eq', 'on') | list | count %}
                  {{ contacts }}/3
                secondary: Otevřené vstupy
                icon: mdi:door-open
                icon_color: >
                  {% set contacts = [
                    states('binary_sensor.0x00158d000315c387_contact'),
                    states('binary_sensor.0x00158d0002e214d4_contact'),
                    states('binary_sensor.0x00158d000315fac9_contact')
                  ] | select('eq', 'on') | list | count %}
                  {% if contacts == 0 %}green{% elif contacts <= 1 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: binary_sensor.0x00158d0002e214d4_contact
              - type: custom:mushroom-template-card
                primary: >
                  {% set motion = [
                    states('binary_sensor.0x00158d00020b5abb_occupancy'),
                    states('binary_sensor.0x00158d0003140d60_occupancy'),
                    states('binary_sensor.0x00158d0002e2697e_occupancy'),
                    states('binary_sensor.0x00158d00030423c7_occupancy')
                  ] | select('eq', 'on') | list | count %}
                  {{ motion }}/4
                secondary: Aktivní pohyb
                icon: mdi:motion-sensor
                icon_color: >
                  {% set motion = [
                    states('binary_sensor.0x00158d00020b5abb_occupancy'),
                    states('binary_sensor.0x00158d0003140d60_occupancy'),
                    states('binary_sensor.0x00158d0002e2697e_occupancy'),
                    states('binary_sensor.0x00158d00030423c7_occupancy')
                  ] | select('eq', 'on') | list | count %}
                  {% if motion == 0 %}grey{% elif motion <= 2 %}green{% else %}orange{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: binary_sensor.0x00158d00020b5abb_occupancy
              - type: custom:mushroom-template-card
                primary: >
                  {% set cameras = [
                    states('binary_sensor.zahrada_bouda_person_occupancy'),
                    states('binary_sensor.ulice_person_occupancy'),
                    states('binary_sensor.zahrada_person_occupancy')
                  ] | select('eq', 'on') | list | count %}
                  {{ cameras }}/3
                secondary: Detekce kamer
                icon: mdi:cctv
                icon_color: >
                  {% set cameras = [
                    states('binary_sensor.zahrada_bouda_person_occupancy'),
                    states('binary_sensor.ulice_person_occupancy'),
                    states('binary_sensor.zahrada_person_occupancy')
                  ] | select('eq', 'on') | list | count %}
                  {% if cameras == 0 %}green{% elif cameras <= 1 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: binary_sensor.zahrada_person_occupancy
      - type: grid
        cards:
          - type: heading
            heading: Kontaktní senzory (Dveře)
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d0002e214d4_contact
                name: Vchod do domu
                icon: mdi:door
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d000315c387_contact
                name: Terasa zahrada
                icon: mdi:door-sliding
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d000315fac9_contact
                name: Bouda zahrada
                icon: mdi:home-variant
                layout: vertical
                tap_action:
                  action: more-info
      - type: grid
        cards:
          - type: heading
            heading: Pohybové senzory (Interiér)
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d00020b5abb_occupancy
                name: Vchod
                icon: mdi:motion-sensor
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d0003140d60_occupancy
                name: Schodiště
                icon: mdi:stairs
                layout: vertical
                tap_action:
                  action: more-info
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d0002e2697e_occupancy
                name: Horní chodba
                icon: mdi:motion-sensor
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.0x00158d00030423c7_occupancy
                name: Pracovna
                icon: mdi:desk
                layout: vertical
                tap_action:
                  action: more-info
      - type: grid
        cards:
          - type: heading
            heading: Kamerové detekce (Exteriér)
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.ulice_person_occupancy
                name: Ulice
                icon: mdi:road
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.zahrada_person_occupancy
                name: Zahrada průchod
                icon: mdi:garden
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.zahrada_bouda_person_occupancy
                name: Zahrada bouda
                icon: mdi:home-variant-outline
                layout: vertical
                tap_action:
                  action: more-info
      - type: grid
        cards:
          - type: heading
            heading: Půdorys s senzory
            heading_style: title
          - type: picture-elements
            image: /local/pudorys_oboje_pozadi6.png
            elements:
              # Kontaktní senzory (dveře)
              - type: state-icon
                entity: binary_sensor.0x00158d0002e214d4_contact
                tap_action:
                  action: more-info
                style:
                  top: 85%
                  left: 29%
                  color: >
                    {% if is_state('binary_sensor.0x00158d0002e214d4_contact', 'on') %}red{% else %}green{% endif %}
                  transform: scale(1.5)
              - type: state-icon
                entity: binary_sensor.0x00158d000315c387_contact
                tap_action:
                  action: more-info
                style:
                  top: 37%
                  left: 11%
                  color: >
                    {% if is_state('binary_sensor.0x00158d000315c387_contact', 'on') %}red{% else %}green{% endif %}
                  transform: scale(1.5)
              - type: state-icon
                entity: binary_sensor.0x00158d000315fac9_contact
                tap_action:
                  action: more-info
                style:
                  top: 12%
                  left: 30%
                  color: >
                    {% if is_state('binary_sensor.0x00158d000315fac9_contact', 'on') %}red{% else %}green{% endif %}
                  transform: scale(1.5)
              # Pohybové senzory (interiér)
              - type: state-icon
                entity: binary_sensor.0x00158d00020b5abb_occupancy
                tap_action:
                  action: more-info
                style:
                  top: 78%
                  left: 30%
                  color: >
                    {% if is_state('binary_sensor.0x00158d00020b5abb_occupancy', 'on') %}orange{% else %}grey{% endif %}
                  transform: scale(1.3)
              - type: state-icon
                entity: binary_sensor.0x00158d0003140d60_occupancy
                tap_action:
                  action: more-info
                style:
                  top: 71%
                  left: 35%
                  color: >
                    {% if is_state('binary_sensor.0x00158d0003140d60_occupancy', 'on') %}orange{% else %}grey{% endif %}
                  transform: scale(1.3)
              - type: state-icon
                entity: binary_sensor.0x00158d0002e2697e_occupancy
                tap_action:
                  action: more-info
                style:
                  top: 64%
                  left: 75%
                  color: >
                    {% if is_state('binary_sensor.0x00158d0002e2697e_occupancy', 'on') %}orange{% else %}grey{% endif %}
                  transform: scale(1.3)
              - type: state-icon
                entity: binary_sensor.0x00158d00030423c7_occupancy
                tap_action:
                  action: more-info
                style:
                  top: 45%
                  left: 76%
                  color: >
                    {% if is_state('binary_sensor.0x00158d00030423c7_occupancy', 'on') %}orange{% else %}grey{% endif %}
                  transform: scale(1.3)
              # Kamery s detekcí osob
              - type: image
                entity: camera.ulice
                image: /local/camera.png
                tap_action:
                  action: more-info
                  entity: binary_sensor.ulice_person_occupancy
                style:
                  top: 87%
                  left: 40%
                  width: 6%
                  transform: none
                  filter: >
                    {% if is_state('binary_sensor.ulice_person_occupancy', 'on') %}drop-shadow(0px 0px 15px red) brightness(150%){% else %}brightness(80%){% endif %}
              - type: image
                entity: camera.zahrada
                image: /local/camera.png
                tap_action:
                  action: more-info
                  entity: binary_sensor.zahrada_person_occupancy
                style:
                  top: 11%
                  left: 40%
                  width: 6%
                  transform: none
                  filter: >
                    {% if is_state('binary_sensor.zahrada_person_occupancy', 'on') %}drop-shadow(0px 0px 15px red) brightness(150%){% else %}brightness(80%){% endif %}
              # Indikátor pro kameru zahrada bouda (není na půdorysu, ale přidáme jako text)
              - type: state-label
                entity: binary_sensor.zahrada_bouda_person_occupancy
                prefix: "Bouda: "
                tap_action:
                  action: more-info
                style:
                  top: 5%
                  left: 85%
                  color: >
                    {% if is_state('binary_sensor.zahrada_bouda_person_occupancy', 'on') %}red{% else %}green{% endif %}
                  font-size: 12px
                  font-weight: bold
                  background-color: rgba(0,0,0,0.5)
                  padding: 2px 6px
                  border-radius: 4px
  - title: Systém & Statistiky
    icon: mdi:chart-box
    type: sections
    max_columns: 4
    path: system-insights
    subview: false
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Systémové informace
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.disk_use_percent'') }}%'
                secondary: Využití disku
                icon: mdi:harddisk
                icon_color: >
                  {% set disk = states('sensor.disk_use_percent') | float %}
                  {% if disk < 70 %}green{% elif disk < 85 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.disk_use_percent
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.current_version_2'') }}'
                secondary: Home Assistant
                icon: mdi:home-assistant
                icon_color: blue
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.current_version_2
              - type: custom:mushroom-template-card
                primary: >
                  {% set updates = states.update | selectattr('state', 'eq', 'on') | list | count %}
                  {{ updates }} dostupných
                secondary: Aktualizace
                icon: mdi:update
                icon_color: >
                  {% set updates = states.update | selectattr('state', 'eq', 'on') | list | count %}
                  {% if updates == 0 %}green{% elif updates <= 3 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: update.frigate_server
      - type: grid
        cards:
          - type: heading
            heading: Zařízení & Přítomnost
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: person.pavel
                name: Pavel
                icon: mdi:account
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: person.petulka
                name: Petulka
                icon: mdi:account-heart
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-template-card
                primary: >
                  {% set home_devices = states.device_tracker | selectattr('state', 'eq', 'home') | list | count %}
                  {{ home_devices }} doma
                secondary: Zařízení
                icon: mdi:devices
                icon_color: >
                  {% set home_devices = states.device_tracker | selectattr('state', 'eq', 'home') | list | count %}
                  {% if home_devices > 5 %}green{% elif home_devices > 2 %}orange{% else %}grey{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: device_tracker.pavel_iphone_new
      - type: grid
        cards:
          - type: heading
            heading: Robotické vysavače
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ state_attr(''vacuum.xiaomi_robot_vacuum_x10'', ''battery_level'') }}%'
                secondary: >
                  Xiaomi X10+ ({{ states('vacuum.xiaomi_robot_vacuum_x10') }})
                icon: mdi:robot-vacuum
                icon_color: >
                  {% set battery = state_attr('vacuum.xiaomi_robot_vacuum_x10', 'battery_level') %}
                  {% if battery > 50 %}green{% elif battery > 20 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: vacuum.xiaomi_robot_vacuum_x10
              - type: custom:mushroom-template-card
                primary: '{{ state_attr(''vacuum.vysavac_horni_patro'', ''battery_level'') }}%'
                secondary: >
                  Horní patro ({{ states('vacuum.vysavac_horni_patro') }})
                icon: mdi:robot-vacuum
                icon_color: >
                  {% set battery = state_attr('vacuum.vysavac_horni_patro', 'battery_level') %}
                  {% if battery > 50 %}green{% elif battery > 20 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: vacuum.vysavac_horni_patro
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ state_attr(''vacuum.xiaomi_robot_vacuum_x10'', ''cleaning_count'') }}'
                secondary: Celkem úklidy X10+
                icon: mdi:counter
                icon_color: blue
                layout: vertical
                tap_action:
                  action: more-info
                  entity: vacuum.xiaomi_robot_vacuum_x10
              - type: custom:mushroom-template-card
                primary: '{{ (state_attr(''vacuum.xiaomi_robot_vacuum_x10'', ''total_cleaning_time'') / 60) | round(0) }}h'
                secondary: Celkový čas úklidu
                icon: mdi:clock
                icon_color: purple
                layout: vertical
                tap_action:
                  action: more-info
                  entity: vacuum.xiaomi_robot_vacuum_x10
              - type: custom:mushroom-template-card
                primary: '{{ state_attr(''vacuum.xiaomi_robot_vacuum_x10'', ''total_cleaned_area'') }}m²'
                secondary: Celková plocha
                icon: mdi:texture-box
                icon_color: green
                layout: vertical
                tap_action:
                  action: more-info
                  entity: vacuum.xiaomi_robot_vacuum_x10
      - type: grid
        cards:
          - type: heading
            heading: Baterie senzorů
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.0x00158d000315fac9_battery'') }}%'
                secondary: Bouda zahrada
                icon: mdi:battery
                icon_color: >
                  {% set battery = states('sensor.0x00158d000315fac9_battery') | float %}
                  {% if battery > 50 %}green{% elif battery > 20 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.0x00158d000315fac9_battery
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.0x00158d000315c387_battery'') }}%'
                secondary: Terasa zahrada
                icon: mdi:battery
                icon_color: >
                  {% set battery = states('sensor.0x00158d000315c387_battery') | float %}
                  {% if battery > 50 %}green{% elif battery > 20 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.0x00158d000315c387_battery
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.0x0c4314fffe5fe450_battery'') }}%'
                secondary: Tesla teplotní čidlo
                icon: mdi:battery
                icon_color: >
                  {% set battery = states('sensor.0x0c4314fffe5fe450_battery') | float %}
                  {% if battery > 50 %}green{% elif battery > 20 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.0x0c4314fffe5fe450_battery
      - type: grid
        cards:
          - type: heading
            heading: Síťové informace
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.bridge_state'') }}'
                secondary: Zigbee Bridge
                icon: mdi:router-wireless
                icon_color: >
                  {% if states('sensor.bridge_state') == 'unknown' %}orange{% else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.bridge_state
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.cert_expiry_timestamp_hass_k8s_sklenarovi_cz'').split(''T'')[0] if states(''sensor.cert_expiry_timestamp_hass_k8s_sklenarovi_cz'') != ''unavailable'' else ''N/A'' }}'
                secondary: SSL certifikát
                icon: mdi:certificate
                icon_color: green
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.cert_expiry_timestamp_hass_k8s_sklenarovi_cz
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.backup_backup_manager_state'') }}'
                secondary: Stav zálohování
                icon: mdi:backup-restore
                icon_color: >
                  {% if states('sensor.backup_backup_manager_state') == 'idle' %}green{% else %}orange{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.backup_backup_manager_state
      - type: grid
        cards:
          - type: heading
            heading: Zajímavé statistiky
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >
                  {% set power_outages = [
                    states('sensor.0x00158d0002e214d4_power_outage_count') | int,
                    states('sensor.0x00158d0003140d60_power_outage_count') | int,
                    states('sensor.0x00158d0002e2697e_power_outage_count') | int
                  ] %}
                  {{ power_outages | max }}
                secondary: Nejvíce výpadků
                icon: mdi:power-plug-off
                icon_color: red
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.0x00158d0002e214d4_power_outage_count
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.illumination_04cf8c983306'') }}lm'
                secondary: Xiaomi Gateway osvit
                icon: mdi:brightness-6
                icon_color: yellow
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.illumination_04cf8c983306
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.chladnicka_fridge_temp'') }}°C / {{ states(''sensor.chladnicka_freezer_temp'') }}°C'
                secondary: Chladnička/Mrazák
                icon: mdi:fridge
                icon_color: blue
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.chladnicka
  - title: Klima & Topení
    icon: mdi:thermometer-lines
    type: sections
    max_columns: 4
    path: climate-control
    subview: false
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Přehled klimatu
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >-
                  {{
                  states('sensor.netatmo_moje_domacnost_weather_station_temperature')
                  }}°C
                secondary: Vnitřní teplota
                icon: mdi:thermometer
                icon_color: >
                  {% set temp =
                  states('sensor.netatmo_moje_domacnost_weather_station_temperature')
                  | float %} {% if temp < 20 %}blue{% elif temp > 25 %}red{%
                  else %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.netatmo_moje_domacnost_weather_station_temperature
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.gw1100a_temperature_1'') }}°C'
                secondary: Venkovní teplota
                icon: mdi:thermometer-lines
                icon_color: >
                  {% set temp = states('sensor.gw1100a_temperature_1') | float
                  %} {% if temp < 10 %}blue{% elif temp > 30 %}red{% else
                  %}orange{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.gw1100a_temperature_1
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.shelly_klimatizace_switch_0_power'') }}W'
                secondary: Spotřeba AC
                icon: mdi:flash
                icon_color: yellow
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.shelly_klimatizace_switch_0_power
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: switch.kotel
                name: Hlavní kotel
                icon: mdi:water-boiler
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: input_boolean.topna_sezona
                name: Topná sezóna
                icon: mdi:snowflake
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-template-card
                primary: |
                  {% set ac_count = [
                    states('climate.klimatizace_pokoj_levy'),
                    states('climate.klimatizace_pokoj_stred'),
                    states('climate.klimatizace_pracovna')
                  ] | select('ne', 'off') | list | count %} {{ ac_count }}/3
                secondary: AC jednotky aktivní
                icon: mdi:air-conditioner
                icon_color: >
                  {% set ac_count = [
                    states('climate.klimatizace_pokoj_levy'),
                    states('climate.klimatizace_pokoj_stred'),
                    states('climate.klimatizace_pracovna')
                  ] | select('ne', 'off') | list | count %} {% if ac_count == 0
                  %}grey{% elif ac_count <= 1 %}green{% elif ac_count <= 2
                  %}orange{% else %}red{% endif %}
                layout: vertical
          - type: heading
            heading: Topení nastavení
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: script.climate_evening
                name: Večerní režim
                icon: mdi:moon-waning-crescent
                layout: vertical
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.climate_evening
              - type: custom:mushroom-entity-card
                entity: script.climate_morning
                name: Ranní režim
                icon: mdi:white-balance-sunny
                layout: vertical
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.climate_morning
              - type: custom:mushroom-entity-card
                entity: input_boolean.zapnout_rano_topeni
                name: Ranní topení
                icon: mdi:weather-sunny-alert
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: input_boolean.vypnout_vecer_topeni
                name: Večerní vypnutí
                icon: mdi:moon-waning-crescent
                layout: vertical
                tap_action:
                  action: toggle
      - type: grid
        cards:
          - type: heading
            heading: Klimatizace
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mushroom-climate-card
                entity: climate.klimatizace_pokoj_levy
                name: Klimatizace Adélka
                show_temperature_control: true
                hvac_modes:
                  - 'off'
                  - heat
                  - cool
                  - auto
                  - dry
                  - fan_only
                layout: default
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-entity-card
                    entity: switch.klimatizace_pokoj_levy_jet_mode
                    name: Jet
                    icon: mdi:rocket-launch
                    layout: vertical
                    tap_action:
                      action: toggle
                  - type: custom:mushroom-entity-card
                    entity: switch.klimatizace_pokoj_levy_ionizer
                    name: Ionizer
                    icon: mdi:air-filter
                    layout: vertical
                    tap_action:
                      action: toggle
                  - type: custom:mushroom-template-card
                    primary: >-
                      {{
                      states('sensor.klimatizace_pokoj_levy_filter_remaining_life')
                      }}%
                    secondary: Filtr
                    icon: mdi:air-filter-outline
                    icon_color: >
                      {% set filter =
                      states('sensor.klimatizace_pokoj_levy_filter_remaining_life')
                      | int %} {% if filter > 50 %}green{% elif filter > 20
                      %}orange{% else %}red{% endif %}
                    layout: vertical
          - type: vertical-stack
            cards:
              - type: custom:mushroom-climate-card
                entity: climate.klimatizace_pokoj_stred
                name: Klimatizace Ložnice
                show_temperature_control: true
                hvac_modes:
                  - 'off'
                  - heat
                  - cool
                  - auto
                  - dry
                  - fan_only
                layout: default
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-entity-card
                    entity: switch.klimatizace_pokoj_stred_jet_mode
                    name: Jet
                    icon: mdi:rocket-launch
                    layout: vertical
                    tap_action:
                      action: toggle
                  - type: custom:mushroom-entity-card
                    entity: switch.klimatizace_pokoj_stred_ionizer
                    name: Ionizer
                    icon: mdi:air-filter
                    layout: vertical
                    tap_action:
                      action: toggle
                  - type: custom:mushroom-template-card
                    primary: >-
                      {{
                      states('sensor.klimatizace_pokoj_stred_filter_remaining_life')
                      }}%
                    secondary: Filtr
                    icon: mdi:air-filter-outline
                    icon_color: >
                      {% set filter =
                      states('sensor.klimatizace_pokoj_stred_filter_remaining_life')
                      | int %} {% if filter > 50 %}green{% elif filter > 20
                      %}orange{% else %}red{% endif %}
                    layout: vertical
          - type: vertical-stack
            cards:
              - type: custom:mushroom-climate-card
                entity: climate.klimatizace_pracovna
                name: Klimatizace Pracovna
                show_temperature_control: true
                hvac_modes:
                  - 'off'
                  - heat
                  - cool
                  - auto
                  - dry
                  - fan_only
                layout: default
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-entity-card
                    entity: switch.klimatizace_pracovna_jet_mode
                    name: Jet
                    icon: mdi:rocket-launch
                    layout: vertical
                    tap_action:
                      action: toggle
                  - type: custom:mushroom-entity-card
                    entity: switch.klimatizace_pracovna_ionizer
                    name: Ionizer
                    icon: mdi:air-filter
                    layout: vertical
                    tap_action:
                      action: toggle
                  - type: custom:mushroom-template-card
                    primary: >-
                      {{
                      states('sensor.klimatizace_pracovna_filter_remaining_life')
                      }}%
                    secondary: Filtr
                    icon: mdi:air-filter-outline
                    icon_color: >
                      {% set filter =
                      states('sensor.klimatizace_pracovna_filter_remaining_life')
                      | int %} {% if filter > 50 %}green{% elif filter > 20
                      %}orange{% else %}red{% endif %}
                    layout: vertical
      - type: grid
        cards:
          - type: heading
            heading: Topení - Přízemí
            heading_style: title
          - type: custom:mushroom-climate-card
            entity: climate.obyvaci_pokoj
            name: Obývací pokoj
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: custom:mushroom-climate-card
            entity: climate.pokoj_dole
            name: Pokoj dole
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: custom:mushroom-climate-card
            entity: climate.koupelna_dole
            name: Koupelna dole
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: custom:mushroom-climate-card
            entity: climate.0x84fd27fffea36224
            name: Chodba
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: heading
            heading: Topení - Patro
            heading_style: title
          - type: custom:mushroom-climate-card
            entity: climate.pokoj_levy
            name: Pokoj levý
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: custom:mushroom-climate-card
            entity: climate.loznice
            name: Ložnice
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: custom:mushroom-climate-card
            entity: climate.pokoj_pravy
            name: Pokoj pravý
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
          - type: custom:mushroom-climate-card
            entity: climate.koupelna_nahore
            name: Koupelna nahoře
            show_temperature_control: true
            hvac_modes:
              - 'off'
              - heat
              - auto
            layout: default
  - type: sections
    max_columns: 4
    title: Domácnost
    path: domacnost
    icon: mdi:home-account
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Přehled domácnosti
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: |
                  {% set devices = [
                    states('binary_sensor.pracka_pere_status'),
                    states('binary_sensor.susicka_susi_status'),
                    states('binary_sensor.chladnicka_door')
                  ] | select('eq', 'on') | list | count %} {{ devices }} aktivní
                secondary: Spotřebiče
                icon: mdi:home-lightning-bolt
                icon_color: >
                  {% set devices = [
                    states('binary_sensor.pracka_pere_status'),
                    states('binary_sensor.susicka_susi_status'),
                    states('binary_sensor.chladnicka_door')
                  ] | select('eq', 'on') | list | count %} {% if devices == 0
                  %}green{% elif devices <= 2 %}orange{% else %}red{% endif %}
                layout: vertical
              - type: custom:mushroom-template-card
                primary: >
                  {% set power =
                  (states('sensor.shellymini_koupelna_nahore_01_power') | float)
                  + (states('sensor.shellymini_koupelna_nahore_02_power') |
                  float) %} {{ power | round(0) }}W
                secondary: Celková spotřeba
                icon: mdi:flash
                icon_color: >
                  {% set power =
                  (states('sensor.shellymini_koupelna_nahore_01_power') | float)
                  + (states('sensor.shellymini_koupelna_nahore_02_power') |
                  float) %} {% if power < 100 %}green{% elif power < 1000
                  %}orange{% else %}red{% endif %}
                layout: vertical
              - type: custom:mushroom-template-card
                primary: >
                  {% set vacuums = [
                    states('vacuum.xiaomi_robot_vacuum_x10'),
                    states('vacuum.vysavac_horni_patro')
                  ] | select('eq', 'cleaning') | list | count %} {% if vacuums >
                  0 %}{{ vacuums }} aktivní{% else %}Nečinné{% endif %}
                secondary: Vysavače
                icon: mdi:robot-vacuum
                icon_color: >
                  {% set vacuums = [
                    states('vacuum.xiaomi_robot_vacuum_x10'),
                    states('vacuum.vysavac_horni_patro')
                  ] | select('eq', 'cleaning') | list | count %} {% if vacuums >
                  0 %}blue{% else %}grey{% endif %}
                layout: vertical
      - type: grid
        cards:
          - type: heading
            heading: Prádelna
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.pracka_pere_status
                name: Pračka
                icon: mdi:washing-machine
                layout: default
                tap_action:
                  action: more-info
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-template-card
                    primary: '{{ states(''sensor.doba_prani_pracky'') }}'
                    secondary: Zbývající čas
                    icon: mdi:timer-outline
                    icon_color: >
                      {% if states('binary_sensor.pracka_pere_status') == 'on'
                      %}blue{% else %}grey{% endif %}
                    layout: vertical
                    tap_action:
                      action: more-info
                      entity: sensor.doba_prani_pracky
                  - type: custom:mushroom-template-card
                    primary: >-
                      {{ states('sensor.shellymini_koupelna_nahore_01_power')
                      }}W
                    secondary: Příkon
                    icon: mdi:flash
                    icon_color: >
                      {% set power =
                      states('sensor.shellymini_koupelna_nahore_01_power') |
                      float %} {% if power < 50 %}grey{% elif power < 1000
                      %}orange{% else %}red{% endif %}
                    layout: vertical
                    tap_action:
                      action: more-info
                      entity: sensor.shellymini_koupelna_nahore_01_power
          - type: vertical-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.susicka_susi_status
                name: Sušička
                icon: mdi:tumble-dryer
                layout: default
                tap_action:
                  action: more-info
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-template-card
                    primary: '{{ states(''sensor.doba_suseni_susicky'') }}'
                    secondary: Zbývající čas
                    icon: mdi:timer-outline
                    icon_color: >
                      {% if states('binary_sensor.susicka_susi_status') == 'on'
                      %}blue{% else %}grey{% endif %}
                    layout: vertical
                    tap_action:
                      action: more-info
                      entity: sensor.doba_suseni_susicky
                  - type: custom:mushroom-template-card
                    primary: >-
                      {{ states('sensor.shellymini_koupelna_nahore_02_power')
                      }}W
                    secondary: Příkon
                    icon: mdi:flash
                    icon_color: >
                      {% set power =
                      states('sensor.shellymini_koupelna_nahore_02_power') |
                      float %} {% if power < 50 %}grey{% elif power < 1000
                      %}orange{% else %}red{% endif %}
                    layout: vertical
                    tap_action:
                      action: more-info
                      entity: sensor.shellymini_koupelna_nahore_02_power
      - type: grid
        cards:
          - type: heading
            heading: Úklid a údržba
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >-
                  {{ state_attr('vacuum.xiaomi_robot_vacuum_x10',
                  'friendly_name') }}
                secondary: >
                  {% set state = states('vacuum.xiaomi_robot_vacuum_x10') %} {%
                  if state == 'cleaning' %}Vysává {% elif state == 'returning'
                  %}Vrací se {% elif state == 'docked' %}V dokovací stanici {%
                  elif state == 'paused' %}Pozastaveno {% else %}{{ state }}{%
                  endif %}
                icon: mdi:robot-vacuum
                icon_color: >
                  {% set state = states('vacuum.xiaomi_robot_vacuum_x10') %} {%
                  if state == 'cleaning' %}blue {% elif state == 'returning'
                  %}orange {% elif state == 'docked' %}green {% else %}grey{%
                  endif %}
                layout: default
                tap_action:
                  action: more-info
                  entity: vacuum.xiaomi_robot_vacuum_x10
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-entity-card
                    entity: vacuum.xiaomi_robot_vacuum_x10
                    name: Start/Stop
                    icon: mdi:play-pause
                    layout: vertical
                    tap_action:
                      action: call-service
                      service: vacuum.start_pause
                      service_data:
                        entity_id: vacuum.xiaomi_robot_vacuum_x10
                  - type: custom:mushroom-entity-card
                    entity: vacuum.xiaomi_robot_vacuum_x10
                    name: Domů
                    icon: mdi:home
                    layout: vertical
                    tap_action:
                      action: call-service
                      service: vacuum.return_to_base
                      service_data:
                        entity_id: vacuum.xiaomi_robot_vacuum_x10
          - type: vertical-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >-
                  {{ state_attr('vacuum.vysavac_horni_patro', 'friendly_name')
                  }}
                secondary: >
                  {% set state = states('vacuum.vysavac_horni_patro') %} {% if
                  state == 'cleaning' %}Vysává {% elif state == 'returning'
                  %}Vrací se {% elif state == 'docked' %}V dokovací stanici {%
                  elif state == 'paused' %}Pozastaveno {% else %}{{ state }}{%
                  endif %}
                icon: mdi:robot-vacuum
                icon_color: >
                  {% set state = states('vacuum.vysavac_horni_patro') %} {% if
                  state == 'cleaning' %}blue {% elif state == 'returning'
                  %}orange {% elif state == 'docked' %}green {% else %}grey{%
                  endif %}
                layout: default
                tap_action:
                  action: more-info
                  entity: vacuum.vysavac_horni_patro
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-entity-card
                    entity: vacuum.vysavac_horni_patro
                    name: Start/Stop
                    icon: mdi:play-pause
                    layout: vertical
                    tap_action:
                      action: call-service
                      service: vacuum.start_pause
                      service_data:
                        entity_id: vacuum.vysavac_horni_patro
                  - type: custom:mushroom-entity-card
                    entity: vacuum.vysavac_horni_patro
                    name: Domů
                    icon: mdi:home
                    layout: vertical
                    tap_action:
                      action: call-service
                      service: vacuum.return_to_base
                      service_data:
                        entity_id: vacuum.vysavac_horni_patro
      - type: grid
        cards:
          - type: heading
            heading: Kuchyňské spotřebiče
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: binary_sensor.chladnicka_door
                name: Dveře chladničky
                icon: mdi:fridge-variant
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-entity-card
                entity: binary_sensor.chladnicka_eco_friendly
                name: Eco režim
                icon: mdi:leaf
                layout: vertical
                tap_action:
                  action: more-info
              - type: custom:mushroom-template-card
                primary: >
                  {% if states('binary_sensor.chladnicka_door') == 'on'
                  %}Otevřené{% else %}Zavřené{% endif %}
                secondary: Status chladničky
                icon: mdi:fridge
                icon_color: >
                  {% if states('binary_sensor.chladnicka_door') == 'on' %}red{%
                  else %}green{% endif %}
                layout: vertical
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: switch.chladnicka_express_cool
                name: Rychlé chlazení
                icon: mdi:snowflake
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: switch.chladnicka_express_mode_2
                name: Rychlé mražení
                icon: mdi:snowflake-melt
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-template-card
                primary: >
                  {% set modes = [
                    states('switch.chladnicka_express_cool'),
                    states('switch.chladnicka_express_mode_2')
                  ] | select('eq', 'on') | list | count %} {% if modes > 0 %}{{
                  modes }} aktivní{% else %}Normální{% endif %}
                secondary: Rychlé režimy
                icon: mdi:lightning-bolt
                icon_color: >
                  {% set modes = [
                    states('switch.chladnicka_express_cool'),
                    states('switch.chladnicka_express_mode_2')
                  ] | select('eq', 'on') | list | count %} {% if modes > 0
                  %}blue{% else %}grey{% endif %}
                layout: vertical
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-number-card
                entity: number.chladnicka_fridge_temperature
                name: Teplota chladničky
                icon: mdi:thermometer
                layout: vertical
                display_mode: slider
              - type: custom:mushroom-number-card
                entity: number.chladnicka_freezer_temperature
                name: Teplota mrazáku
                icon: mdi:thermometer-minus
                layout: vertical
                display_mode: slider
      - type: grid
        cards:
          - type: heading
            heading: Zábava a média
            heading_style: title
          - type: vertical-stack
            cards:
              - type: custom:mushroom-media-player-card
                entity: media_player.lg_webos_tv_oled55c11lb
                name: LG OLED TV
                use_media_info: true
                show_volume_level: true
                media_controls:
                  - play_pause_stop
                  - previous
                  - next
                  - volume_mute
                  - volume_set
              - type: horizontal-stack
                cards:
                  - type: custom:mushroom-template-card
                    primary: >
                      {% if states('media_player.lg_webos_tv_oled55c11lb') ==
                      'on' %}Zapnuto{% else %}Vypnuto{% endif %}
                    secondary: TV Status
                    icon: mdi:television
                    icon_color: >
                      {% if states('media_player.lg_webos_tv_oled55c11lb') ==
                      'on' %}green{% else %}grey{% endif %}
                    layout: vertical
                    tap_action:
                      action: toggle
                      entity: media_player.lg_webos_tv_oled55c11lb
                  - type: custom:mushroom-template-card
                    primary: >-
                      {{ state_attr('media_player.lg_webos_tv_oled55c11lb',
                      'volume_level') | multiply(100) | round(0) }}%
                    secondary: Hlasitost
                    icon: mdi:volume-high
                    icon_color: blue
                    layout: vertical
                    tap_action:
                      action: more-info
                      entity: media_player.lg_webos_tv_oled55c11lb
      - type: grid
        cards:
          - type: heading
            heading: Mobilita a bezpečnost
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.cesta_do_cloudfield_z_domova'') }}'
                secondary: Cesta do práce
                icon: mdi:office-building
                icon_color: >
                  {% set time = states('sensor.cesta_do_cloudfield_z_domova') |
                  regex_replace(' min', '') | int %} {% if time < 20 %}green{%
                  elif time < 40 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.cesta_do_cloudfield_z_domova
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.cesta_na_parkoviste'') }}'
                secondary: Cesta na parkoviště
                icon: mdi:parking
                icon_color: >
                  {% set time = states('sensor.cesta_na_parkoviste') |
                  regex_replace(' min', '') | int %} {% if time < 10 %}green{%
                  elif time < 20 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.cesta_na_parkoviste
              - type: custom:mushroom-template-card
                primary: >
                  {% set work_time =
                  states('sensor.cesta_do_cloudfield_z_domova') |
                  regex_replace(' min', '') | int %} {% set parking_time =
                  states('sensor.cesta_na_parkoviste') | regex_replace(' min',
                  '') | int %} {% if work_time < 25 and parking_time < 15
                  %}Dobrá{% elif work_time < 35 and parking_time < 25
                  %}Střední{% else %}Špatná{% endif %}
                secondary: Dopravní situace
                icon: mdi:car
                icon_color: >
                  {% set work_time =
                  states('sensor.cesta_do_cloudfield_z_domova') |
                  regex_replace(' min', '') | int %} {% set parking_time =
                  states('sensor.cesta_na_parkoviste') | regex_replace(' min',
                  '') | int %} {% if work_time < 25 and parking_time < 15
                  %}green{% elif work_time < 35 and parking_time < 25 %}orange{%
                  else %}red{% endif %}
                layout: vertical
  - title: surveillance
    cards: []
    icon: mdi:camera
    type: sections
    max_columns: 2
    subview: false
    sections:
      - type: grid
        cards:
          - show_state: true
            show_name: true
            camera_view: auto
            type: picture-entity
            image: https://demo.home-assistant.io/stub_config/bedroom.png
            entity: camera.ulice
            camera_image: camera.ulice
          - type: custom:advanced-camera-card
            cameras:
              - camera_entity: camera.ulice_frigate
            view:
              default: clips
            menu:
              style: outside
              buttons:
                camera_ui:
                  enabled: false
                snapshots:
                  enabled: false
                live:
                  enabled: false
                substreams:
                  enabled: false
                cameras:
                  enabled: false
                recordings:
                  enabled: true
                iris:
                  enabled: false
            media_viewer:
              draggable: true
              zoomable: true
              snapshot_click_plays_clip: true
            timeline: {}
            dimensions:
              height: 80vh
      - type: grid
        cards:
          - show_state: true
            show_name: true
            camera_view: auto
            type: picture-entity
            image: https://demo.home-assistant.io/stub_config/bedroom.png
            entity: camera.reolink_zahrada_fluent
            camera_image: camera.zahrada
            name: Zahrada
          - type: custom:advanced-camera-card
            cameras:
              - camera_entity: camera.zahrada_bouda
            view:
              default: clips
            menu:
              style: outside
              buttons:
                camera_ui:
                  enabled: false
                snapshots:
                  enabled: false
                live:
                  enabled: false
                substreams:
                  enabled: false
                cameras:
                  enabled: false
                recordings:
                  enabled: true
                iris:
                  enabled: false
            media_viewer:
              draggable: true
              zoomable: true
              snapshot_click_plays_clip: true
            timeline: {}
            dimensions:
              height: 80vh
      - type: grid
        cards:
          - type: heading
            heading: New section
          - show_state: true
            show_name: true
            camera_view: auto
            type: picture-entity
            image: https://demo.home-assistant.io/stub_config/bedroom.png
            entity: camera.zahrada
            camera_image: camera.zahrada
          - type: custom:advanced-camera-card
            cameras:
              - camera_entity: camera.zahrada_frigate
            view:
              default: clips
            menu:
              style: outside
              buttons:
                camera_ui:
                  enabled: false
                snapshots:
                  enabled: false
                live:
                  enabled: false
                substreams:
                  enabled: false
                cameras:
                  enabled: false
                recordings:
                  enabled: true
                iris:
                  enabled: false
            media_viewer:
              draggable: true
              zoomable: true
              snapshot_click_plays_clip: true
            timeline: {}
            dimensions:
              height: 80vh
  - title: Home
    type: sidebar
    icon: mdi:solar-power-variant
    cards:
      - type: custom:power-flow-card-plus
        title: Aktuální stav FVE
        entities:
          grid:
            entity:
              consumption: sensor.energy_buy
              production: sensor.energy_sell
            display_state: two_way
            color_circle: true
          solar:
            entity: sensor.pv_power
          battery:
            entity: sensor.battery_power
            state_of_charge: sensor.battery_state_of_charge
            display_state: one_way
            color_circle: true
          home:
            color_icon: true
            entity: sensor.house_consumption
            override_state: true
        watt_threshold: 10000
        clickable_entities: true
        display_zero_lines:
          mode: show
          transparency: 50
          grey_color:
            - 189
            - 189
            - 189
        view_layout:
          position: main
        use_new_flow_rate_model: true
      - type: horizontal-stack
        cards:
          - type: gauge
            entity: sensor.battery_state_of_charge
            name: Stav nabití
            min: 0
            max: 100
            unit: '%'
            severity:
              green: 70
              yellow: 40
              red: 20
          - type: entities
            title: Nastavení baterie
            show_header_toggle: false
            entities:
              - entity: number.goodwe_depth_of_discharge_on_grid
                name: Max. vybití (on-grid)
              - entity: select.goodwe_inverter_operation_mode
                name: Režim
              - entity: number.goodwe_grid_export_limit
                name: Limit přetoku
      - type: vertical-stack
        title: Statistiky
        view_layout:
          position: main
        cards:
          - type: energy-date-selection
          - type: horizontal-stack
            cards:
              - type: energy-self-sufficiency-gauge
              - type: energy-solar-consumed-gauge
              - type: energy-grid-neutrality-gauge
          - type: energy-distribution
          - type: energy-usage-graph
          - type: energy-solar-graph
      - type: horizontal-stack
        cards:
          - type: history-graph
            title: Produkce FVE
            entities:
              - entity: sensor.pv1_power
                name: Panely východ
              - entity: sensor.pv2_power
                name: Panely západ
              - entity: sensor.pv_power
                name: Panely celkem
            hours_to_show: 24
          - type: history-graph
            title: Teploty
            hours_to_show: 24
            entities:
              - entity: sensor.battery_temperature
                name: Teplota baterie
              - entity: sensor.inverter_temperature_air
                name: Teplota střídače (vzduch)
              - entity: sensor.inverter_temperature_radiator
                name: Teplota střídače (chladič)
      - type: custom:sankey-chart
        title: Tok Energie
        unit_prefix: k
        round: 2
        show_names: true
        energy_date_selection: true
        show_icons: false
        sections:
          - entities:
              - type: entity
                entity_id: sensor.total_pv_generation
                name: Solar
                color: '#ff9800'
                children:
                  - sensor.total_battery_charge
                  - sensor.energy_sell_daily
                  - sensor.house_consumption_daily
              - type: entity
                entity_id: sensor.energy_buy_daily
                name: Grid
                color: '#488fc2'
                children:
                  - sensor.house_consumption_daily
                  - sensor.total_battery_charge
              - type: entity
                entity_id: sensor.total_battery_discharge
                name: Baterie
                color: '#4db6ac'
                children:
                  - sensor.house_consumption_daily
                  - sensor.energy_sell_daily
          - entities:
              - type: entity
                entity_id: sensor.house_consumption_daily
                name: Dům
                color: lightblue
              - type: entity
                entity_id: sensor.total_battery_charge
                name: Baterie
                color: '#f06292'
                children: []
              - type: entity
                entity_id: sensor.energy_sell_daily
                name: Přetok
                color: '#8353d1'
                children: []
  - type: sections
    max_columns: 4
    title: Zahrada
    path: zahrada
    icon: mdi:forest-outline
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Počasí
            heading_style: title
          - show_current: true
            show_forecast: true
            type: weather-forecast
            entity: weather.openweathermap
            forecast_type: daily
          - type: custom:horizon-card
            entity: sun.sun
            name: Pozice slunce
      - type: grid
        cards:
          - type: heading
            heading: Bouda - Ovládání
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: tile
                entity: switch.sonoff_4ch01_1
                name: Zásuvky uvnitř
                icon: mdi:power-socket-eu
                vertical: true
                color: blue
              - type: tile
                entity: switch.sonoff_4ch01_1_sonoff_4ch01_2
                name: Světla
                icon: mdi:lightbulb
                vertical: true
                color: yellow
              - type: tile
                entity: switch.sonoff_4ch01_1_sonoff_4ch01_4
                name: Zásuvky venku
                icon: mdi:power-socket-eu
                vertical: true
                color: green
          - type: tile
            entity: binary_sensor.0x00158d000315fac9_contact
            name: Dveře bouda
            icon: mdi:door
            color: red
          - type: heading
            heading: Rychlé akce
            heading_style: title
          - type: horizontal-stack
            cards:
              - show_name: true
                show_icon: true
                type: button
                name: Zapnout vše v boudě
                icon: mdi:power
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.bouda_all_on
              - type: button
                name: Vypnout vše v boudě
                icon: mdi:power-off
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.bouda_all_off
              - show_name: true
                show_icon: true
                type: button
                name: Noční režim zahrady
                icon: mdi:weather-night
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.garden_night_mode
      - type: grid
        cards:
          - type: heading
            heading: Zahradní podmínky
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: gauge
                entity: sensor.gw1100a_temperature_1
                name: Venkovní teplota
                unit: °C
                min: -10
                max: 40
                needle: true
                severity:
                  green: 15
                  yellow: 25
                  red: 35
              - type: gauge
                entity: sensor.gw1100a_humidity_1
                name: Vlhkost vzduchu
                unit: '%'
                min: 0
                max: 100
                needle: true
                severity:
                  green: 40
                  yellow: 30
                  red: 0
          - type: custom:mini-graph-card
            name: Teploty - 48h
            entities:
              - entity: sensor.gw1100a_temperature_1
                name: Venku
                color: '#03a9f4'
              - entity: sensor.gw1100a_temperature_3
                name: Bazén
                color: '#00bcd4'
            hours_to_show: 48
            points_per_hour: 2
            line_width: 2
            show:
              labels: true
              average: true
              extrema: true
      - type: grid
        cards:
          - type: heading
            heading: Bazén - Ovládání
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: tile
                entity: switch.sonoff_4ch01_1_sonoff_4ch01_3
                name: Filtrace bazénu
                icon: mdi:pump
                vertical: true
                color: blue
                features_position: bottom
              - type: tile
                entity: input_boolean.solar_pool
                name: Solární ohřev
                icon: mdi:solar-panel
                vertical: true
                color: orange
                features_position: bottom
          - type: horizontal-stack
            cards:
              - type: gauge
                entity: sensor.gw1100a_temperature_3
                name: Teplota vody
                unit: °C
                min: 0
                max: 40
                needle: true
                severity:
                  green: 22
                  yellow: 28
                  red: 32
              - type: entity
                entity: sensor.bazenove_cerpadlo_dnes_celkem
                name: Spotřeba dnes
                icon: mdi:flash
          - type: history-graph
            title: Stav filtrace - 24h
            entities:
              - entity: switch.sonoff_4ch01_1_sonoff_4ch01_3
                name: Filtrace bazénu
            hours_to_show: 24
          - type: custom:mini-graph-card
            name: Provoz filtrace - 24h
            entities:
              - entity: sensor.bazenove_cerpadlo_dnes_celkem
                name: Spotřeba energie
                color: '#ff9800'
                show_state: true
            hours_to_show: 24
            points_per_hour: 4
            line_width: 2
            show:
              labels: true
              average: true
      - type: grid
        cards:
          - type: heading
            heading: Historie & Grafy
            heading_style: title
  - title: Historie & Analytika
    path: history
    icon: mdi:chart-line-variant
    type: sections
    max_columns: 4
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Klimatické trendy
            heading_style: title
          - type: custom:mini-graph-card
            name: Teploty - Klimatizované místnosti
            entities:
              - entity: sensor.teplota_pokoj_levy
                name: Adélka
                color: '#e91e63'
              - entity: sensor.teplota_loznice
                name: Ložnice
                color: '#9c27b0'
              - entity: sensor.teplota_pokoj_pravy
                name: Pracovna
                color: '#3f51b5'
            hours_to_show: 48
            points_per_hour: 4
            line_width: 2
            show:
              labels: true
              average: true
              extrema: true
          - type: custom:mini-graph-card
            name: Teploty - Topené místnosti
            entities:
              - entity: sensor.teplota_obyvaci_pokoj
                name: Obývák
                color: '#ff9800'
              - entity: sensor.teplota_pokoj_dole
                name: Pokoj dole
                color: '#ff5722'
              - entity: sensor.teplota_koupelna_nahore
                name: Koupelna
                color: '#795548'
            hours_to_show: 48
            points_per_hour: 4
            line_width: 2
            show:
              labels: true
              average: true
          - type: custom:mini-graph-card
            name: Venkovní vs. Vnitřní teplota
            entities:
              - entity: sensor.gw1100a_temperature_1
                name: Venku
                color: '#03a9f4'
              - entity: sensor.netatmo_moje_domacnost_weather_station_temperature
                name: Uvnitř
                color: '#4caf50'
            hours_to_show: 72
            points_per_hour: 2
            line_width: 2
            show:
              labels: true
              average: true
              extrema: true
      - type: grid
        cards:
          - type: heading
            heading: Kvalita vzduchu
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: gauge
                entity: sensor.netatmo_moje_domacnost_weather_station_co2
                name: CO₂ Obývák
                unit: ppm
                min: 400
                max: 3000
                needle: true
                severity:
                  green: 1000
                  yellow: 2000
                  red: 2500
              - type: gauge
                entity: >-
                  sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2
                name: CO₂ Ložnice
                unit: ppm
                min: 400
                max: 3000
                needle: true
                severity:
                  green: 1000
                  yellow: 2000
                  red: 2500
              - type: gauge
                entity: >-
                  sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2
                name: CO₂ Pracovna
                unit: ppm
                min: 400
                max: 3000
                needle: true
                severity:
                  green: 1000
                  yellow: 2000
                  red: 2500
          - type: custom:mini-graph-card
            name: Úrovně CO₂ - Trendy
            entities:
              - entity: sensor.netatmo_moje_domacnost_weather_station_co2
                name: Obývák
                color: '#ff9800'
              - entity: >-
                  sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_co2
                name: Ložnice
                color: '#9c27b0'
              - entity: >-
                  sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_co2
                name: Pracovna
                color: '#3f51b5'
            hours_to_show: 48
            points_per_hour: 4
            color_thresholds:
              - value: 1000
                color: '#ffeb3b'
              - value: 2000
                color: '#f44336'
            show:
              labels: true
              average: true
          - type: custom:mini-graph-card
            name: Vlhkost vzduchu
            entities:
              - entity: sensor.netatmo_moje_domacnost_weather_station_humidity
                name: Obývák
                color: '#00bcd4'
              - entity: >-
                  sensor.weather_station_weather_station_weather_station_loznice_netatmo_sensor_humidity
                name: Ložnice
                color: '#009688'
              - entity: >-
                  sensor.weather_station_weather_station_weather_station_pracovna_netatmo_sensor_humidity
                name: Pracovna
                color: '#4caf50'
            hours_to_show: 168
            aggregate_func: avg
            group_by: hour
            show:
              graph: bar
              labels: true
      - type: grid
        cards:
          - type: heading
            heading: Energetická analýza
            heading_style: title
          - type: custom:mini-graph-card
            name: Solární výroba vs. spotřeba
            entities:
              - entity: sensor.pv_power
                name: Výroba
                color: '#ff9800'
              - entity: sensor.house_consumption
                name: Spotřeba
                color: '#03a9f4'
            hours_to_show: 48
            points_per_hour: 4
            line_width: 2
            show:
              labels: true
              average: true
              extrema: true
          - type: custom:mini-graph-card
            name: Spotřeba klimatizace
            entities:
              - entity: sensor.shelly_klimatizace_switch_0_power
                name: Aktuální příkon
                color: '#f44336'
            hours_to_show: 24
            points_per_hour: 4
            line_width: 2
            show:
              labels: true
              average: true
          - type: statistics-graph
            entities:
              - sensor.shelly_klimatizace_switch_0_energy
            stat_types:
              - sum
            period: day
            days_to_show: 7
            title: Spotřeba klimatizace (7 dní)
      - type: grid
        cards:
          - type: heading
            heading: Venkovní podmínky
            heading_style: title
          - type: custom:mini-graph-card
            name: Venkovní teplota a vlhkost
            entities:
              - entity: sensor.gw1100a_temperature_1
                name: Teplota
                color: '#ff5722'
                y_axis: primary
              - entity: sensor.gw1100a_humidity_1
                name: Vlhkost
                color: '#00bcd4'
                y_axis: secondary
            hours_to_show: 72
            points_per_hour: 2
            line_width: 2
            show:
              labels: true
              average: true
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.gw1100a_temperature_1'') }}°C'
                secondary: Venkovní teplota
                icon: mdi:thermometer
                icon_color: >
                  {% set temp = states('sensor.gw1100a_temperature_1') | float
                  %} {% if temp < 10 %}blue{% elif temp > 30 %}red{% else
                  %}orange{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.gw1100a_temperature_1
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.gw1100a_humidity_1'') }}%'
                secondary: Venkovní vlhkost
                icon: mdi:water-percent
                icon_color: >
                  {% set hum = states('sensor.gw1100a_humidity_1') | float %} {%
                  if hum < 30 %}red{% elif hum > 70 %}blue{% else %}green{%
                  endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.gw1100a_humidity_1
              - type: custom:mushroom-template-card
                primary: '{{ states(''sensor.gw1100a_temperature_3'') }}°C'
                secondary: Teplota bazénu
                icon: mdi:pool-thermometer
                icon_color: >
                  {% set temp = states('sensor.gw1100a_temperature_3') | float
                  %} {% if temp < 20 %}blue{% elif temp > 28 %}red{% else
                  %}green{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.gw1100a_temperature_3
      - type: grid
        cards:
          - type: heading
            heading: Údržba a výkonnost
            heading_style: title
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-template-card
                primary: >-
                  {{
                  states('sensor.klimatizace_pokoj_levy_filter_remaining_life')
                  }}%
                secondary: Filtr Adélka
                icon: mdi:air-filter
                icon_color: >
                  {% set filter =
                  states('sensor.klimatizace_pokoj_levy_filter_remaining_life')
                  | int %} {% if filter > 50 %}green{% elif filter > 20
                  %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.klimatizace_pokoj_levy_filter_remaining_life
              - type: custom:mushroom-template-card
                primary: >-
                  {{
                  states('sensor.klimatizace_pokoj_stred_filter_remaining_life')
                  }}%
                secondary: Filtr Ložnice
                icon: mdi:air-filter
                icon_color: >
                  {% set filter =
                  states('sensor.klimatizace_pokoj_stred_filter_remaining_life')
                  | int %} {% if filter > 50 %}green{% elif filter > 20
                  %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.klimatizace_pokoj_stred_filter_remaining_life
              - type: custom:mushroom-template-card
                primary: >-
                  {{ states('sensor.klimatizace_pracovna_filter_remaining_life')
                  }}%
                secondary: Filtr Pracovna
                icon: mdi:air-filter
                icon_color: >
                  {% set filter =
                  states('sensor.klimatizace_pracovna_filter_remaining_life') |
                  int %} {% if filter > 50 %}green{% elif filter > 20 %}orange{%
                  else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: more-info
                  entity: sensor.klimatizace_pracovna_filter_remaining_life
          - type: custom:mini-graph-card
            name: Efektivita klimatizace
            entities:
              - entity: sensor.shelly_klimatizace_switch_0_power
                name: Příkon
                color: '#f44336'
              - entity: sensor.netatmo_moje_domacnost_weather_station_temperature
                name: Vnitřní teplota
                color: '#4caf50'
                y_axis: secondary
            hours_to_show: 24
            points_per_hour: 4
            line_width: 2
            show:
              labels: true
              average: true
  - type: sections
    max_columns: 3
    icon: mdi:home-thermometer
    title: Klima
    path: klima
    badges:
      - type: custom:mushroom-template-badge
        content: |
          {% set ac_count = [
            states('climate.klimatizace_pokoj_levy'),
            states('climate.klimatizace_pokoj_stred'),
            states('climate.klimatizace_pracovna')
          ] | select('ne', 'off') | list | count %} {{ ac_count }}/3 AC aktivní
        icon: mdi:air-conditioner
        color: >
          {% set ac_count = [
            states('climate.klimatizace_pokoj_levy'),
            states('climate.klimatizace_pokoj_stred'),
            states('climate.klimatizace_pracovna')
          ] | select('ne', 'off') | list | count %} {% if ac_count == 0 %}grey{%
          elif ac_count <= 1 %}green{% elif ac_count <= 2 %}orange{% else
          %}red{% endif %}
        tap_action:
          action: navigate
          navigation_path: /lovelace/history
      - type: custom:mushroom-template-badge
        content: '{{ states(''sensor.shelly_klimatizace_switch_0_power'') }}W'
        icon: mdi:flash
        color: >
          {% set power = states('sensor.shelly_klimatizace_switch_0_power') |
          float %} {% if power < 100 %}green{% elif power < 1000 %}orange{% else
          %}red{% endif %}
        tap_action:
          action: more-info
          entity: sensor.shelly_klimatizace_switch_0_power
      - type: custom:mushroom-template-badge
        content: |
          {% set filters = [
            states('sensor.klimatizace_pokoj_levy_filter_remaining_life') | int,
            states('sensor.klimatizace_pokoj_stred_filter_remaining_life') | int,
            states('sensor.klimatizace_pracovna_filter_remaining_life') | int
          ] %} {{ filters | min }}% filtr
        icon: mdi:air-filter-outline
        color: >
          {% set filters = [
            states('sensor.klimatizace_pokoj_levy_filter_remaining_life') | int,
            states('sensor.klimatizace_pokoj_stred_filter_remaining_life') | int,
            states('sensor.klimatizace_pracovna_filter_remaining_life') | int
          ] %} {% set min_filter = filters | min %} {% if min_filter > 50
          %}green{% elif min_filter > 20 %}orange{% else %}red{% endif %}
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Klimatizace Adélka
            heading_style: subtitle
          - type: thermostat
            entity: climate.klimatizace_pokoj_levy
            show_current_as_primary: false
            name: Klimatizace Adélka
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: horizontal-stack
            cards:
              - type: tile
                entity: switch.klimatizace_pokoj_levy_jet_mode
                name: Jet mode
                vertical: true
              - type: tile
                entity: switch.klimatizace_pokoj_levy_ionizer
                name: Ionizer
                vertical: true
                tap_action:
                  action: toggle
          - type: horizontal-stack
            cards:
              - type: tile
                entity: sensor.klimatizace_pokoj_levy_filter_remaining_life
                name: Filtr
                color: >
                  {% set filter =
                  states('sensor.klimatizace_pokoj_levy_filter_remaining_life')
                  | int %} {% if filter > 50 %}green{% elif filter > 20
                  %}orange{% else %}red{% endif %}
              - type: tile
                entity: sensor.klimatizace_pokoj_levy_sleep_time
                name: Sleep
      - type: grid
        cards:
          - type: heading
            heading: Klimatizace Ložnice
            heading_style: subtitle
          - type: thermostat
            entity: climate.klimatizace_pokoj_stred
            name: Klimatizace Ložnice
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: horizontal-stack
            cards:
              - type: tile
                entity: switch.klimatizace_pokoj_stred_jet_mode
                vertical: true
                name: Jet Mode
              - type: tile
                entity: switch.klimatizace_pokoj_stred_ionizer
                name: Ionizer
                vertical: true
                tap_action:
                  action: toggle
          - type: horizontal-stack
            cards:
              - type: tile
                entity: sensor.klimatizace_pokoj_stred_filter_remaining_life
                name: Filtr
                color: >
                  {% set filter =
                  states('sensor.klimatizace_pokoj_stred_filter_remaining_life')
                  | int %} {% if filter > 50 %}green{% elif filter > 20
                  %}orange{% else %}red{% endif %}
              - type: tile
                entity: sensor.klimatizace_pokoj_stred_sleep_time
                name: Sleep
      - type: grid
        cards:
          - type: heading
            heading: Klimatizace Pracovna
            heading_style: subtitle
          - type: thermostat
            entity: climate.klimatizace_pracovna
            name: Klimatizace Pracovna
            features:
              - type: climate-hvac-modes
              - type: climate-fan-modes
                style: dropdown
              - type: climate-swing-modes
                style: dropdown
          - type: horizontal-stack
            cards:
              - type: tile
                entity: switch.klimatizace_pracovna_jet_mode
                name: Jet Mode
                vertical: true
              - type: tile
                entity: switch.klimatizace_pracovna_ionizer
                name: Ionizer
                vertical: true
                tap_action:
                  action: toggle
          - type: horizontal-stack
            cards:
              - type: tile
                entity: sensor.klimatizace_pracovna_filter_remaining_life
                name: Filtr
                color: >
                  {% set filter =
                  states('sensor.klimatizace_pracovna_filter_remaining_life') |
                  int %} {% if filter > 50 %}green{% elif filter > 20 %}orange{%
                  else %}red{% endif %}
              - type: tile
                entity: sensor.klimatizace_pracovna_sleep_time
                name: Sleep
    cards: []
  - title: Topení
    icon: mdi:heat-wave
    type: sections
    max_columns: 4
    subview: false
    sections:
      - type: grid
        cards:
          - type: heading
            heading: Topení - Přízemí
            heading_style: subtitle
          - type: thermostat
            entity: climate.obyvaci_pokoj
            features:
              - type: climate-hvac-modes
            show_current_as_primary: false
            name: Obývací pokoj
          - type: thermostat
            entity: climate.pokoj_dole
            features:
              - type: climate-hvac-modes
            name: Pokoj dole
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.koupelna_dole
            name: Koupelna dole
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.0x84fd27fffea36224
            name: Chodba
      - type: grid
        cards:
          - type: heading
            heading: Topení - Patro
            heading_style: subtitle
          - type: thermostat
            entity: climate.loznice
            features:
              - type: climate-hvac-modes
            show_current_as_primary: false
            name: Ložnice
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.pokoj_pravy
            name: Pokoj pravý
          - type: thermostat
            entity: climate.pokoj_levy
            features:
              - type: climate-hvac-modes
            name: Pokoj levý
          - type: thermostat
            features:
              - type: climate-hvac-modes
            entity: climate.koupelna_nahore
            name: Koupelna nahoře
      - type: grid
        cards:
          - type: heading
            heading: Automatizace a plánování
            heading_style: subtitle
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: switch.kotel
                name: Hlavní kotel
                icon: mdi:water-boiler
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: input_boolean.topna_sezona
                name: Topná sezóna
                icon: mdi:snowflake
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-template-card
                primary: >
                  {% set heating_count = [
                    states('climate.obyvaci_pokoj'),
                    states('climate.pokoj_dole'),
                    states('climate.koupelna_dole'),
                    states('climate.loznice'),
                    states('climate.koupelna_nahore'),
                    states('climate.0x84fd27fffea36224'),
                    states('climate.pokoj_levy'),
                    states('climate.pokoj_pravy')
                  ] | select('eq', 'heat') | list | count %} {{ heating_count
                  }}/8
                secondary: Místnosti topí
                icon: mdi:radiator
                icon_color: >
                  {% set heating_count = [
                    states('climate.obyvaci_pokoj'),
                    states('climate.pokoj_dole'),
                    states('climate.koupelna_dole'),
                    states('climate.loznice'),
                    states('climate.koupelna_nahore'),
                    states('climate.0x84fd27fffea36224'),
                    states('climate.pokoj_levy'),
                    states('climate.pokoj_pravy')
                  ] | select('eq', 'heat') | list | count %} {% if heating_count
                  == 0 %}grey{% elif heating_count <= 3 %}green{% elif
                  heating_count <= 6 %}orange{% else %}red{% endif %}
                layout: vertical
                tap_action:
                  action: navigate
                  navigation_path: /lovelace/history
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: script.climate_evening
                name: Večerní režim
                icon: mdi:moon-waning-crescent
                layout: vertical
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.climate_evening
              - type: custom:mushroom-entity-card
                entity: script.climate_morning
                name: Ranní režim
                icon: mdi:white-balance-sunny
                layout: vertical
                tap_action:
                  action: call-service
                  service: script.turn_on
                  service_data:
                    entity_id: script.climate_morning
              - type: custom:mushroom-entity-card
                entity: input_boolean.zapnout_rano_topeni
                name: Ranní topení
                icon: mdi:weather-sunny-alert
                layout: vertical
                tap_action:
                  action: toggle
          - type: horizontal-stack
            cards:
              - type: custom:mushroom-entity-card
                entity: input_boolean.vypnout_vecer_topeni
                name: Večerní vypnutí
                icon: mdi:moon-waning-crescent
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: input_boolean.zapnout_koupelna_nahore_topeni
                name: Koupelna nahoře
                icon: mdi:bathtub
                layout: vertical
                tap_action:
                  action: toggle
              - type: custom:mushroom-entity-card
                entity: input_boolean.zapnout_koupelna_dole_topeni
                name: Koupelna dole
                icon: mdi:bathtub-outline
                layout: vertical
                tap_action:
                  action: toggle
  - type: sections
    path: ''
    max_columns: 4
    icon: mdi:account-group
    sections:
      - type: grid
        cards:
          - type: heading
            heading_style: subtitle
          - type: vertical-stack
            cards:
              - type: markdown
                content: '## Climate Actions'
              - type: horizontal-stack
                cards:
                  - show_name: true
                    show_icon: true
                    type: button
                    name: Evening Mode
                    icon: mdi:moon-waning-crescent
                    tap_action:
                      action: call-service
                      service: script.climate_evening
                  - type: button
                    name: Morning Mode
                    icon: mdi:white-balance-sunny
                    tap_action:
                      action: call-service
                      service: script.climate_morning
                  - type: button
                    name: Toggle Boiler
                    entity: switch.kotel
                    icon: mdi:radiator
                    tap_action:
                      action: toggle
                  - type: button
                    name: Heat Season
                    entity: input_boolean.topna_sezona
                    icon: mdi:snowflake
                    tap_action:
                      action: toggle
              - type: markdown
                content: '## Scheduled Settings'
              - type: entities
                entities:
                  - entity: input_boolean.zapnout_rano_topeni
                    name: Morning Heating On?
                    icon: mdi:weather-sunny-alert
                  - entity: input_boolean.vypnout_vecer_topeni
                    name: Evening Heating Off?
                    icon: mdi:moon-waning-crescent
              - type: markdown
                content: '## Temperatures – Morning'
              - type: entities
                entities:
                  - input_number.morning_loznice
                  - input_number.morning_pokoj_vlevo
                  - input_number.morning_pokoj_dole
                  - input_number.morning_obyvak
              - type: markdown
                content: '## Temperatures – Evening & Sleep'
              - type: entities
                entities:
                  - input_number.sleep_loznice
                  - input_number.sleep_pokoj_vlevo
                  - input_number.sleep_pokoj_dole
                  - input_number.sleep_obyvak
              - type: markdown
                content: '## Bathroom Heating'
              - type: entities
                entities:
                  - input_number.koupani_koupelna_nahore
                  - input_number.koupani_koupelna_dole
                  - input_boolean.zapnout_koupelna_nahore_topeni
                  - input_boolean.zapnout_koupelna_dole_topeni
                  - input_datetime.start_heat_koupelna_nahore
                  - input_datetime.start_heat_koupelna_dole
