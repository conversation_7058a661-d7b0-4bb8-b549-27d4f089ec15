#!/usr/bin/env python3
"""
Home Assistant Documentation Generator

This script connects to a Home Assistant instance via REST API and generates
a comprehensive markdown documentation of all devices, entities, and their states.
"""

import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse
import sys
import os
import shutil
from collections import defaultdict
from dotenv import load_dotenv

class HomeAssistantDocGenerator:
    def __init__(self, base_url: str, token: str, output_dir: str = "docs"):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.output_dir = output_dir
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
    def fetch_config(self) -> Optional[Dict[str, Any]]:
        """Fetch Home Assistant configuration"""
        try:
            response = requests.get(f'{self.base_url}/api/config', headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching config: {e}")
            return None
    
    def fetch_states(self) -> Optional[List[Dict[str, Any]]]:
        """Fetch all entity states"""
        try:
            response = requests.get(f'{self.base_url}/api/states', headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching states: {e}")
            return None
    
    def fetch_state(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Fetch specific entity state"""
        try:
            response = requests.get(f'{self.base_url}/api/states/{entity_id}', headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching state for {entity_id}: {e}")
            return None
    
    def group_entities_by_domain(self, states: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group entities by their domain (sensor, switch, light, etc.)"""
        grouped = defaultdict(list)
        for entity in states:
            domain = entity['entity_id'].split('.')[0]
            grouped[domain].append(entity)
        return dict(sorted(grouped.items()))
    
    def extract_device_info(self, entity: Dict[str, Any]) -> Optional[str]:
        """Extract device ID from entity attributes"""
        attributes = entity.get('attributes', {})
        return attributes.get('device_id')
    
    def group_entities_by_device(self, entities: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group entities by their device based on entity naming patterns"""
        device_groups = defaultdict(list)
        ungrouped = []
        
        # Common device patterns
        device_patterns = [
            # Pattern: device_name_component_detail
            (r'^(.+?)_(?:sensor|switch|select|number|binary_sensor|climate)_', 1),
            # Pattern: device.component
            (r'^([^.]+)\.(.+?)_', 1),
            # Specific patterns for known devices
            (r'^(xiaomi_robot_vacuum_[^_]+)', 1),
            (r'^(klimatizace_[^_]+)', 1),
            (r'^(shelly[^_]*_[^_]+)', 1),
            (r'^(sonoff_[^_]+)', 1),
            (r'^(netatmo_[^_]+)', 1),
            (r'^(goodwe_[^_]+)', 1),
            (r'^(gw1100a)', 1),
            (r'^(chladnicka)', 1),
            (r'^(pracka|susicka)', 1),
            # Generic pattern: everything before first underscore
            (r'^([^_]+)', 1),
        ]
        
        import re
        
        for entity in entities:
            entity_id = entity['entity_id']
            # Remove domain prefix
            entity_name = entity_id.split('.', 1)[1] if '.' in entity_id else entity_id
            
            device_found = False
            
            # Try to match with device patterns
            for pattern, group in device_patterns:
                match = re.match(pattern, entity_name)
                if match:
                    device_name = match.group(group)
                    # Clean up device name
                    device_name = device_name.replace('_', ' ').title()
                    device_groups[device_name].append(entity)
                    device_found = True
                    break
            
            # If no pattern matches, use friendly name or put in ungrouped
            if not device_found:
                friendly_name = entity.get('attributes', {}).get('friendly_name', '')
                if friendly_name:
                    # Try to extract device from friendly name
                    parts = friendly_name.split()
                    if len(parts) >= 2:
                        device_name = ' '.join(parts[:2])
                        device_groups[device_name].append(entity)
                    else:
                        ungrouped.append(entity)
                else:
                    ungrouped.append(entity)
        
        # Add ungrouped entities
        if ungrouped:
            device_groups['Other'] = ungrouped
        
        # Sort device groups by name
        return dict(sorted(device_groups.items()))
    
    def format_attributes(self, attributes: Dict[str, Any], indent: str = "    ") -> str:
        """Format entity attributes in a readable way"""
        if not attributes:
            return f"{indent}*No attributes*"
        
        lines = []
        for key, value in sorted(attributes.items()):
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, indent=2, ensure_ascii=False)
                # Indent multi-line values
                value_str = value_str.replace('\n', f'\n{indent}  ')
            else:
                value_str = str(value)
            lines.append(f"{indent}- **{key}**: {value_str}")
        return '\n'.join(lines)
    
    def prepare_output_directory(self):
        """Clean and create output directory"""
        if os.path.exists(self.output_dir):
            print(f"Cleaning existing directory: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        os.makedirs(self.output_dir)
        print(f"Created output directory: {self.output_dir}")
    
    def generate_overview_page(self, config: Dict[str, Any], grouped_entities: Dict[str, List[Dict[str, Any]]]) -> str:
        """Generate overview page with links to domain pages"""
        md_lines = []
        
        # Header
        md_lines.append("# Home Assistant Instance Documentation")
        md_lines.append(f"\n*Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        md_lines.append("")
        
        # Configuration Section
        md_lines.append("## Configuration Overview")
        md_lines.append("")
        if config:
            md_lines.append(f"- **Version**: {config.get('version', 'Unknown')}")
            md_lines.append(f"- **Location**: {config.get('location_name', 'Not set')}")
            md_lines.append(f"- **Latitude**: {config.get('latitude', 'Not set')}")
            md_lines.append(f"- **Longitude**: {config.get('longitude', 'Not set')}")
            md_lines.append(f"- **Elevation**: {config.get('elevation', 'Not set')} m")
            md_lines.append(f"- **Time Zone**: {config.get('time_zone', 'Not set')}")
            md_lines.append(f"- **Unit System**: {config.get('unit_system', {}).get('name', 'Not set')}")
            md_lines.append(f"- **Currency**: {config.get('currency', 'Not set')}")
            md_lines.append(f"- **Country**: {config.get('country', 'Not set')}")
            md_lines.append(f"- **Language**: {config.get('language', 'Not set')}")
            
            # Components
            components = config.get('components', [])
            if components:
                md_lines.append(f"\n### Loaded Components ({len(components)})")
                md_lines.append("")
                # Group components by first letter for better readability
                components_by_letter = defaultdict(list)
                for comp in sorted(components):
                    components_by_letter[comp[0].upper()].append(comp)
                
                for letter, comps in sorted(components_by_letter.items()):
                    md_lines.append(f"**{letter}**: {', '.join(comps)}")
                md_lines.append("")
        else:
            md_lines.append("*Configuration data not available*")
        
        # Entity Summary
        md_lines.append("\n## Entity Summary by Domain")
        md_lines.append("")
        md_lines.append("| Domain | Count | Documentation |")
        md_lines.append("|--------|-------|---------------|")
        
        total = 0
        for domain, entities in grouped_entities.items():
            count = len(entities)
            total += count
            # Create link to domain file
            domain_file = f"{domain}.md"
            md_lines.append(f"| {domain.title()} | {count} | [View Details](./{domain_file}) |")
        
        md_lines.append(f"| **Total** | **{total}** | - |")
        md_lines.append("")
        
        # Quick Stats
        md_lines.append("## Quick Statistics")
        md_lines.append("")
        
        # Find most common device types
        all_devices = defaultdict(int)
        for domain, entities in grouped_entities.items():
            device_groups = self.group_entities_by_device(entities)
            for device_name, device_entities in device_groups.items():
                if device_name != 'Other':
                    all_devices[device_name] += len(device_entities)
        
        if all_devices:
            md_lines.append("### Top 10 Devices by Entity Count")
            md_lines.append("")
            md_lines.append("| Device | Entity Count |")
            md_lines.append("|--------|--------------|")
            
            top_devices = sorted(all_devices.items(), key=lambda x: x[1], reverse=True)[:10]
            for device, count in top_devices:
                md_lines.append(f"| {device} | {count} |")
            
            md_lines.append("")
        
        return '\n'.join(md_lines)
    
    def generate_domain_page(self, domain: str, entities: List[Dict[str, Any]]) -> str:
        """Generate documentation page for a specific domain"""
        md_lines = []
        
        # Header
        md_lines.append(f"# {domain.title()} Entities")
        md_lines.append(f"\n[← Back to Overview](./index.md)")
        md_lines.append("")
        md_lines.append(f"*Total entities: {len(entities)}*")
        md_lines.append("")
        
        # Group entities by device
        device_groups = self.group_entities_by_device(entities)
        
        # Table of Contents
        if len(device_groups) > 3:
            md_lines.append("## Table of Contents")
            md_lines.append("")
            for device_name in device_groups.keys():
                anchor = device_name.lower().replace(' ', '-').replace('.', '')
                md_lines.append(f"- [{device_name}](#{anchor})")
            md_lines.append("")
        
        # Entities by device
        for device_name, device_entities in device_groups.items():
            anchor = device_name.lower().replace(' ', '-').replace('.', '')
            
            if len(device_entities) > 1:
                # Device header for grouped entities
                md_lines.append(f"## {device_name} {{#{anchor}}}")
                md_lines.append(f"*{len(device_entities)} entities*")
                md_lines.append("")
                
                # Sort entities within device
                sorted_device_entities = sorted(
                    device_entities,
                    key=lambda e: e['entity_id']
                )
                
                for entity in sorted_device_entities:
                    entity_id = entity['entity_id']
                    state = entity['state']
                    attributes = entity.get('attributes', {})
                    friendly_name = attributes.get('friendly_name', entity_id)
                    
                    # Compact format for grouped entities
                    md_lines.append(f"### {friendly_name}")
                    md_lines.append(f"- **Entity ID**: `{entity_id}` | **State**: `{state}`")
                    
                    # Only show key attributes
                    if attributes:
                        display_attrs = {k: v for k, v in attributes.items() 
                                       if k not in ['friendly_name', 'icon'] and v is not None}
                        if display_attrs:
                            # Show only important attributes inline
                            important_attrs = []
                            for key in ['device_class', 'unit_of_measurement', 'state_class']:
                                if key in display_attrs:
                                    important_attrs.append(f"{key}: {display_attrs[key]}")
                            if important_attrs:
                                md_lines.append(f"- **Key Attributes**: {', '.join(important_attrs)}")
                            
                            # Show other attributes if present
                            other_attrs = {k: v for k, v in display_attrs.items() 
                                         if k not in ['device_class', 'unit_of_measurement', 'state_class']}
                            if other_attrs:
                                md_lines.append("- **Other Attributes**:")
                                md_lines.append(self.format_attributes(other_attrs))
                    md_lines.append("")
            else:
                # Single entity - use original detailed format
                entity = device_entities[0]
                entity_id = entity['entity_id']
                state = entity['state']
                attributes = entity.get('attributes', {})
                friendly_name = attributes.get('friendly_name', entity_id)
                
                # Entity header
                md_lines.append(f"## {friendly_name} {{#{anchor}}}")
                md_lines.append(f"- **Entity ID**: `{entity_id}`")
                md_lines.append(f"- **State**: `{state}`")
                
                # Last changed/updated
                last_changed = entity.get('last_changed', 'Unknown')
                last_updated = entity.get('last_updated', 'Unknown')
                if last_changed != 'Unknown':
                    try:
                        last_changed_dt = datetime.fromisoformat(last_changed.replace('Z', '+00:00'))
                        last_changed = last_changed_dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                if last_updated != 'Unknown':
                    try:
                        last_updated_dt = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                        last_updated = last_updated_dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                
                md_lines.append(f"- **Last Changed**: {last_changed}")
                md_lines.append(f"- **Last Updated**: {last_updated}")
                
                # Attributes
                if attributes:
                    # Remove friendly_name from attributes display
                    display_attrs = {k: v for k, v in attributes.items() if k != 'friendly_name'}
                    if display_attrs:
                        md_lines.append("- **Attributes**:")
                        md_lines.append(self.format_attributes(display_attrs))
                
                md_lines.append("")
        
        return '\n'.join(md_lines)
    
    def save_markdown(self, content: str, filename: str):
        """Save markdown content to file"""
        filepath = os.path.join(self.output_dir, filename)
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  Saved: {filename}")
        except IOError as e:
            print(f"Error saving file {filename}: {e}")
            sys.exit(1)
    
    def run(self):
        """Main execution method"""
        print("Preparing output directory...")
        self.prepare_output_directory()
        
        print("Fetching Home Assistant configuration...")
        config = self.fetch_config()
        
        print("Fetching all entity states...")
        states = self.fetch_states()
        
        if not config and not states:
            print("Error: Could not fetch any data from Home Assistant")
            sys.exit(1)
        
        print("Generating documentation...")
        
        # Group entities by domain
        grouped = self.group_entities_by_domain(states or [])
        
        # Generate overview page
        print("Creating overview page...")
        overview_content = self.generate_overview_page(config or {}, grouped)
        self.save_markdown(overview_content, "index.md")
        
        # Generate domain-specific pages
        print(f"Creating {len(grouped)} domain pages...")
        for domain, entities in grouped.items():
            domain_content = self.generate_domain_page(domain, entities)
            self.save_markdown(domain_content, f"{domain}.md")
        
        # Print summary
        print(f"\nSummary:")
        print(f"  - Generated documentation for {len(states or [])} entities")
        print(f"  - Created {len(grouped) + 1} markdown files")
        print(f"  - Output directory: {self.output_dir}/")
        print("\nDone!")


def main():
    # Load environment variables from .env file
    load_dotenv()
    
    parser = argparse.ArgumentParser(
        description='Generate markdown documentation for Home Assistant instance'
    )
    parser.add_argument(
        '--url',
        default=os.getenv('HA_URL', 'https://hass.k8s.sklenarovi.cz'),
        help='Home Assistant URL (default: https://hass.k8s.sklenarovi.cz or HA_URL env var)'
    )
    parser.add_argument(
        '--token',
        default=os.getenv('HA_TOKEN'),
        help='Home Assistant Long-Lived Access Token (can also use HA_TOKEN env var)'
    )
    parser.add_argument(
        '--output-dir',
        default='docs',
        help='Output directory for documentation files (default: docs)'
    )
    
    args = parser.parse_args()
    
    # Check if token is provided
    if not args.token:
        print("Error: No token provided. Set HA_TOKEN in .env file or use --token argument.")
        sys.exit(1)
    
    # Create generator instance and run
    generator = HomeAssistantDocGenerator(args.url, args.token, args.output_dir)
    generator.run()


if __name__ == '__main__':
    main()